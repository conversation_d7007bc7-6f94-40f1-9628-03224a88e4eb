'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  UserIcon,
  KeyIcon,
  ShieldCheckIcon,
  BellIcon,
  EyeIcon,
  EyeSlashIcon,
  TrashIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/Toast';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { useSubscription } from '@/hooks/useSubscription';

export default function SettingsPage() {
  const router = useRouter();
  const { user } = useSubscription();
  const supabase = createSupabaseBrowserClient();
  const { success, error: toastError } = useToast();

  const [activeSection, setActiveSection] = useState('account');
  const [loading, setLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showResetModal, setShowResetModal] = useState(false);
  const [isResetLoading, setIsResetLoading] = useState(false);
  const [showChangeEmailModal, setShowChangeEmailModal] = useState(false);
  const [isEmailLoading, setIsEmailLoading] = useState(false);
  const [emailChangeData, setEmailChangeData] = useState({
    newEmail: '',
    currentPassword: ''
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    securityAlerts: true,
    usageAlerts: true,
    marketingEmails: false
  });

  const [userStats, setUserStats] = useState({
    configCount: 0,
    apiKeyCount: 0,
    userApiKeyCount: 0
  });

  const sidebarItems = [
    { id: 'account', label: 'Account settings', icon: UserIcon },
    { id: 'notifications', label: 'Notifications', icon: BellIcon },
    { id: 'security', label: 'Security', icon: ShieldCheckIcon },
    { id: 'danger', label: 'Danger zone', icon: TrashIcon }
  ];

  // Load user data and stats
  useEffect(() => {
    if (user) {
      loadUserStats();
      loadNotificationSettings();
    }
  }, [user]);

  const loadUserStats = async () => {
    try {
      // Get configuration count
      const configResponse = await fetch('/api/custom-configs');
      const configData = configResponse.ok ? await configResponse.json() : [];
      const configs = Array.isArray(configData) ? configData : [];

      // Get user API keys count
      const userKeysResponse = await fetch('/api/user-api-keys');
      const userKeysData = userKeysResponse.ok ? await userKeysResponse.json() : { api_keys: [] };
      const userKeys = userKeysData.api_keys || [];

      // Get total API keys count across all configs
      let totalApiKeys = 0;
      for (const config of configs) {
        try {
          const keysResponse = await fetch(`/api/custom-configs/${config.id}/keys`);
          const keysData = keysResponse.ok ? await keysResponse.json() : { api_keys: [] };
          totalApiKeys += (keysData.api_keys || []).length;
        } catch (error) {
          console.error('Error loading keys for config:', config.id, error);
        }
      }

      setUserStats({
        configCount: configs.length,
        apiKeyCount: totalApiKeys,
        userApiKeyCount: userKeys.length
      });
    } catch (error) {
      console.error('Error loading user stats:', error);
    }
  };

  const loadNotificationSettings = async () => {
    try {
      // Load from user metadata or local storage
      const savedSettings = localStorage.getItem('notification_settings');
      if (savedSettings) {
        setNotificationSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Error loading notification settings:', error);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!passwordData.currentPassword.trim()) {
      toastError('Current password is required');
      return;
    }

    if (!passwordData.newPassword.trim()) {
      toastError('New password is required');
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toastError('New passwords do not match');
      return;
    }

    if (passwordData.newPassword.length < 8) {
      toastError('Password must be at least 8 characters long');
      return;
    }

    if (passwordData.newPassword === passwordData.currentPassword) {
      toastError('New password must be different from current password');
      return;
    }

    setLoading(true);

    try {
      // First verify current password by attempting to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user?.email || '',
        password: passwordData.currentPassword
      });

      if (signInError) {
        toastError('Current password is incorrect');
        setLoading(false);
        return;
      }

      // If current password is correct, update to new password
      const { error: updateError } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      });

      if (updateError) throw updateError;

      console.log('Password updated successfully, showing toast...');
      success('Password updated successfully', 'Your password has been changed successfully.');
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (error: any) {
      console.error('Password update error:', error);
      toastError('Failed to update password', error.message || 'Please try again');
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationUpdate = async () => {
    setLoading(true);

    try {
      // Save to local storage and user metadata
      localStorage.setItem('notification_settings', JSON.stringify(notificationSettings));

      // Also save to user metadata for persistence across devices
      const { error } = await supabase.auth.updateUser({
        data: {
          notification_preferences: notificationSettings
        }
      });

      if (error) throw error;

      success('Notification preferences saved successfully');
    } catch (error: any) {
      console.error('Notification update error:', error);
      toastError('Failed to save notification preferences');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordReset = async () => {
    if (!user?.email) {
      toastError('No email address found');
      return;
    }

    setIsResetLoading(true);
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(user.email, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      });

      if (error) throw error;

      success('Password reset email sent!', 'Check your inbox for instructions to reset your password.');
      setShowResetModal(false);
    } catch (error: any) {
      console.error('Password reset error:', error);
      toastError('Failed to send reset email', error.message || 'Please try again.');
    } finally {
      setIsResetLoading(false);
    }
  };

  const handleEmailChange = async () => {
    if (!emailChangeData.newEmail.trim()) {
      toastError('Please enter a new email address');
      return;
    }

    if (!emailChangeData.currentPassword.trim()) {
      toastError('Current password is required to change email');
      return;
    }

    if (emailChangeData.newEmail === user?.email) {
      toastError('New email must be different from current email');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailChangeData.newEmail)) {
      toastError('Please enter a valid email address');
      return;
    }

    setIsEmailLoading(true);
    try {
      // First verify current password
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user?.email || '',
        password: emailChangeData.currentPassword
      });

      if (signInError) {
        toastError('Current password is incorrect');
        setIsEmailLoading(false);
        return;
      }

      // Update email
      const { error: updateError } = await supabase.auth.updateUser({
        email: emailChangeData.newEmail
      });

      if (updateError) throw updateError;

      success('Email change initiated!', 'Check both your old and new email addresses for confirmation instructions.');
      setShowChangeEmailModal(false);
      setEmailChangeData({ newEmail: '', currentPassword: '' });
    } catch (error: any) {
      console.error('Email change error:', error);
      toastError('Failed to change email', error.message || 'Please try again.');
    } finally {
      setIsEmailLoading(false);
    }
  };

  const handleAccountDeletion = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Call our account deletion API
      const response = await fetch('/api/user/delete-account', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete account');
      }

      success('Account deleted successfully. You will be redirected to the homepage.');

      // Sign out and redirect
      await supabase.auth.signOut();
      setTimeout(() => {
        window.location.href = '/';
      }, 2000);

    } catch (error: any) {
      console.error('Account deletion error:', error);
      toastError('Failed to delete account', error.message || 'Please contact support.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen w-full bg-[#040716] text-white overflow-x-hidden">
      {/* Header Section - Following analytics/playground design */}
      <div className="border-b border-gray-800/50 bg-gradient-to-r from-gray-900/20 to-gray-800/20">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">
                Account Settings ⚙️
              </h1>
              <p className="text-gray-400 text-lg">
                Manage your account settings and preferences
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-800/50 bg-gray-900/30">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <nav className="-mb-px flex space-x-8">
            {sidebarItems.map((item) => {
              const Icon = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => setActiveSection(item.id)}
                  className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeSection === item.id
                      ? 'border-orange-500 text-orange-400'
                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  {item.label}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Content Area */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8 bg-red-500/20 border-4 border-yellow-400">
        <div className="w-full">
            {activeSection === 'account' && (
              <div className="space-y-8 max-w-7xl mx-auto">
                {/* Email Address Section */}
                <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-orange-500/10 rounded-lg">
                      <EnvelopeIcon className="h-5 w-5 text-orange-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-white">Email address</h3>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div className="flex-1">
                      <p className="text-gray-300">Your email address is <span className="font-medium text-white">{user?.email}</span></p>
                      <p className="text-sm text-gray-400 mt-1">This is used for login and important notifications</p>
                    </div>
                    <button
                      className="px-4 py-2 text-sm font-medium text-orange-400 hover:text-orange-300 hover:bg-orange-500/10 rounded-md transition-colors border border-orange-500/20 hover:border-orange-500/40 shrink-0"
                      onClick={() => setShowChangeEmailModal(true)}
                    >
                      Change
                    </button>
                  </div>
                </div>

                {/* Account Information */}
                <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-2 bg-blue-500/10 rounded-lg">
                      <UserIcon className="h-5 w-5 text-blue-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-white">Account Information</h3>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium text-white mb-1">Full Name</h4>
                        <p className="text-gray-300">
                          {user?.user_metadata?.full_name || user?.user_metadata?.first_name + ' ' + user?.user_metadata?.last_name || 'Not set'}
                        </p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-white mb-1">Account Created</h4>
                        <p className="text-gray-300">
                          {user?.created_at ? new Date(user.created_at).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          }) : 'Unknown'}
                        </p>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium text-white mb-1">Account Usage</h4>
                        <div className="space-y-1 text-sm text-gray-300">
                          <p>{userStats.configCount} configurations</p>
                          <p>{userStats.apiKeyCount} API keys</p>
                          <p>{userStats.userApiKeyCount} user-generated keys</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Password Section */}
                <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-2 bg-green-500/10 rounded-lg">
                      <KeyIcon className="h-5 w-5 text-green-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-white">Password</h3>
                  </div>
                  <form onSubmit={handlePasswordChange} className="space-y-6">
                    <div>
                      <Label htmlFor="currentPassword" className="text-sm font-medium text-white">Current password</Label>
                      <div className="relative mt-2">
                        <input
                          id="currentPassword"
                          type={showCurrentPassword ? "text" : "password"}
                          value={passwordData.currentPassword}
                          onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                          placeholder="••••••••••"
                          className="w-full pr-10 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                        >
                          {showCurrentPassword ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                        </button>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="newPassword" className="text-sm font-medium text-white">New password</Label>
                        <div className="relative mt-2">
                          <input
                            id="newPassword"
                            type={showNewPassword ? "text" : "password"}
                            value={passwordData.newPassword}
                            onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                            placeholder="••••••••••"
                            className="w-full pr-10 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
                            required
                          />
                          <button
                            type="button"
                            onClick={() => setShowNewPassword(!showNewPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                          >
                            {showNewPassword ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                          </button>
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="confirmPassword" className="text-sm font-medium text-white">Confirm new password</Label>
                        <div className="relative mt-2">
                          <input
                            id="confirmPassword"
                            type={showConfirmPassword ? "text" : "password"}
                            value={passwordData.confirmPassword}
                            onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                            placeholder="••••••••••"
                            className="w-full pr-10 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200"
                            required
                          />
                          <button
                            type="button"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                          >
                            {showConfirmPassword ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-300">
                        Can't remember your current password? <button
                          type="button"
                          className="text-orange-400 hover:text-orange-300 font-medium"
                          onClick={() => setShowResetModal(true)}
                        >
                          Reset your password
                        </button>
                      </div>
                      <Button type="submit" disabled={loading} className="bg-orange-600 hover:bg-orange-700 text-white">
                        {loading ? 'Saving...' : 'Save password'}
                      </Button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            {activeSection === 'notifications' && (
              <div className="space-y-8 max-w-7xl mx-auto">
                {/* Email Notifications */}
                <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-2 bg-blue-500/10 rounded-lg">
                      <BellIcon className="h-5 w-5 text-blue-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-white">Notification Preferences</h3>
                  </div>
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-white">Email Notifications</h4>
                        <p className="text-sm text-gray-400">Receive important updates via email</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={notificationSettings.emailNotifications}
                          onChange={(e) => setNotificationSettings(prev => ({ ...prev, emailNotifications: e.target.checked }))}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                      </label>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-white">Security Alerts</h4>
                        <p className="text-sm text-gray-400">Get notified about security events</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={notificationSettings.securityAlerts}
                          onChange={(e) => setNotificationSettings(prev => ({ ...prev, securityAlerts: e.target.checked }))}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                      </label>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-white">Usage Alerts</h4>
                        <p className="text-sm text-gray-400">Notifications about API usage and limits</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={notificationSettings.usageAlerts}
                          onChange={(e) => setNotificationSettings(prev => ({ ...prev, usageAlerts: e.target.checked }))}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                      </label>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-white">Marketing Emails</h4>
                        <p className="text-sm text-gray-400">Product updates and feature announcements</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={notificationSettings.marketingEmails}
                          onChange={(e) => setNotificationSettings(prev => ({ ...prev, marketingEmails: e.target.checked }))}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                      </label>
                    </div>
                  </div>
                  <div className="mt-6 pt-6 border-t border-gray-700">
                    <Button
                      onClick={handleNotificationUpdate}
                      disabled={loading}
                      className="bg-orange-600 hover:bg-orange-700 text-white"
                    >
                      {loading ? 'Saving...' : 'Save Preferences'}
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {activeSection === 'security' && (
              <div className="space-y-8 max-w-7xl mx-auto">
                {/* Security Overview */}
                <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-2 bg-green-500/10 rounded-lg">
                      <ShieldCheckIcon className="h-5 w-5 text-green-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-white">Security Settings</h3>
                  </div>
                  <div className="space-y-6">
                    <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                      <div className="flex items-center gap-3">
                        <ShieldCheckIcon className="h-5 w-5 text-green-400" />
                        <div>
                          <h4 className="text-sm font-medium text-green-400">Account Security Status</h4>
                          <p className="text-sm text-green-300">Your account is secure and protected</p>
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-gray-800/50 rounded-lg p-4">
                        <h4 className="text-sm font-medium text-white mb-2">Last Login</h4>
                        <p className="text-sm text-gray-300">
                          {user?.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Unknown'}
                        </p>
                      </div>
                      <div className="bg-gray-800/50 rounded-lg p-4">
                        <h4 className="text-sm font-medium text-white mb-2">Account Type</h4>
                        <p className="text-sm text-gray-300">Email & Password</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeSection === 'danger' && (
              <div className="space-y-8 max-w-7xl mx-auto">
                {/* Danger Zone */}
                <div className="bg-red-900/20 backdrop-blur-sm rounded-lg border border-red-800/50 p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-2 bg-red-500/10 rounded-lg">
                      <TrashIcon className="h-5 w-5 text-red-400" />
                    </div>
                    <h3 className="text-lg font-semibold text-white">Danger Zone</h3>
                  </div>
                  <div className="space-y-6">
                    <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                      <h4 className="text-sm font-medium text-red-400 mb-2">⚠️ Account Deletion</h4>
                      <p className="text-sm text-red-300 mb-4">
                        Once you delete your account, there is no going back. This will permanently delete your account,
                        configurations, API keys, and all associated data.
                      </p>
                      <Button
                        onClick={() => setShowDeleteModal(true)}
                        className="bg-red-600 hover:bg-red-700 text-white"
                      >
                        Delete Account
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
        </div>
      </div>

      {/* Change Email Modal */}
      {showChangeEmailModal && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 border border-gray-800 rounded-xl shadow-2xl max-w-md w-full p-6">
            <div className="flex items-center gap-3 mb-4">
              <EnvelopeIcon className="h-6 w-6 text-orange-400" />
              <h3 className="text-lg font-semibold text-white">Change Email Address</h3>
            </div>
            <div className="space-y-4">
              <div>
                <Label htmlFor="newEmail" className="text-sm font-medium text-white">New Email Address</Label>
                <input
                  id="newEmail"
                  type="email"
                  value={emailChangeData.newEmail}
                  onChange={(e) => setEmailChangeData(prev => ({ ...prev, newEmail: e.target.value }))}
                  placeholder="Enter new email address"
                  className="w-full mt-2 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              </div>
              <div>
                <Label htmlFor="currentPasswordEmail" className="text-sm font-medium text-white">Current Password</Label>
                <input
                  id="currentPasswordEmail"
                  type="password"
                  value={emailChangeData.currentPassword}
                  onChange={(e) => setEmailChangeData(prev => ({ ...prev, currentPassword: e.target.value }))}
                  placeholder="Enter current password"
                  className="w-full mt-2 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="flex gap-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowChangeEmailModal(false)}
                className="flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50"
              >
                Cancel
              </Button>
              <Button
                onClick={handleEmailChange}
                disabled={isEmailLoading}
                className="flex-1 bg-orange-600 hover:bg-orange-700 text-white"
              >
                {isEmailLoading ? 'Changing...' : 'Change Email'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Reset Password Modal */}
      {showResetModal && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 border border-gray-800 rounded-xl shadow-2xl max-w-md w-full p-6">
            <div className="flex items-center gap-3 mb-4">
              <KeyIcon className="h-6 w-6 text-blue-400" />
              <h3 className="text-lg font-semibold text-white">Reset Password</h3>
            </div>
            <p className="text-gray-300 mb-4">
              We'll send a password reset link to your email address.
            </p>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowResetModal(false)}
                className="flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50"
              >
                Cancel
              </Button>
              <Button
                onClick={handlePasswordReset}
                disabled={isResetLoading}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isResetLoading ? 'Sending...' : 'Send Reset Link'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Account Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 border border-red-800/50 rounded-xl shadow-2xl max-w-md w-full p-6">
            <div className="flex items-center gap-3 mb-4">
              <TrashIcon className="h-6 w-6 text-red-400" />
              <h3 className="text-lg font-semibold text-white">Delete Account</h3>
            </div>
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-4">
              <p className="text-red-300 text-sm">
                ⚠️ <strong>This action cannot be undone.</strong> This will permanently delete your account,
                all configurations, API keys, and associated data.
              </p>
            </div>
            <p className="text-gray-300 mb-6">
              Are you sure you want to delete your account? Type "DELETE" to confirm.
            </p>
            <input
              type="text"
              placeholder="Type DELETE to confirm"
              className="w-full px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-red-500 focus:border-transparent mb-4"
              onChange={(e) => {
                // You can add confirmation logic here
              }}
            />
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowDeleteModal(false)}
                className="flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50"
              >
                Cancel
              </Button>
              <Button
                onClick={handleAccountDeletion}
                disabled={loading}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white"
              >
                {loading ? 'Deleting...' : 'Delete Account'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

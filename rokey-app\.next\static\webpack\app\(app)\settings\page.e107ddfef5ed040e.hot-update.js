"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(app)/settings/page",{

/***/ "(app-pages-browser)/./src/app/(app)/settings/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/(app)/settings/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,KeyIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,KeyIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BellIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,KeyIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,KeyIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,KeyIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,KeyIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/KeyIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,KeyIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BellIcon,EnvelopeIcon,EyeIcon,EyeSlashIcon,KeyIcon,ShieldCheckIcon,TrashIcon,UserIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.tsx\");\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_7__.useSubscription)();\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_6__.createSupabaseBrowserClient)();\n    const { success, error: toastError } = (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('account');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCurrentPassword, setShowCurrentPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNewPassword, setShowNewPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteModal, setShowDeleteModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showResetModal, setShowResetModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isResetLoading, setIsResetLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showChangeEmailModal, setShowChangeEmailModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEmailLoading, setIsEmailLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [emailChangeData, setEmailChangeData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        newEmail: '',\n        currentPassword: ''\n    });\n    const [passwordData, setPasswordData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n    });\n    const [notificationSettings, setNotificationSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        emailNotifications: true,\n        securityAlerts: true,\n        usageAlerts: true,\n        marketingEmails: false\n    });\n    const [userStats, setUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        configCount: 0,\n        apiKeyCount: 0,\n        userApiKeyCount: 0\n    });\n    const sidebarItems = [\n        {\n            id: 'account',\n            label: 'Account settings',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: 'notifications',\n            label: 'Notifications',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: 'security',\n            label: 'Security',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: 'danger',\n            label: 'Danger zone',\n            icon: _barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        }\n    ];\n    // Load user data and stats\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            if (user) {\n                loadUserStats();\n                loadNotificationSettings();\n            }\n        }\n    }[\"SettingsPage.useEffect\"], [\n        user\n    ]);\n    const loadUserStats = async ()=>{\n        try {\n            // Get configuration count\n            const configResponse = await fetch('/api/custom-configs');\n            const configData = configResponse.ok ? await configResponse.json() : [];\n            const configs = Array.isArray(configData) ? configData : [];\n            // Get user API keys count\n            const userKeysResponse = await fetch('/api/user-api-keys');\n            const userKeysData = userKeysResponse.ok ? await userKeysResponse.json() : {\n                api_keys: []\n            };\n            const userKeys = userKeysData.api_keys || [];\n            // Get total API keys count across all configs\n            let totalApiKeys = 0;\n            for (const config of configs){\n                try {\n                    const keysResponse = await fetch(\"/api/custom-configs/\".concat(config.id, \"/keys\"));\n                    const keysData = keysResponse.ok ? await keysResponse.json() : {\n                        api_keys: []\n                    };\n                    totalApiKeys += (keysData.api_keys || []).length;\n                } catch (error) {\n                    console.error('Error loading keys for config:', config.id, error);\n                }\n            }\n            setUserStats({\n                configCount: configs.length,\n                apiKeyCount: totalApiKeys,\n                userApiKeyCount: userKeys.length\n            });\n        } catch (error) {\n            console.error('Error loading user stats:', error);\n        }\n    };\n    const loadNotificationSettings = async ()=>{\n        try {\n            // Load from user metadata or local storage\n            const savedSettings = localStorage.getItem('notification_settings');\n            if (savedSettings) {\n                setNotificationSettings(JSON.parse(savedSettings));\n            }\n        } catch (error) {\n            console.error('Error loading notification settings:', error);\n        }\n    };\n    const handlePasswordChange = async (e)=>{\n        e.preventDefault();\n        if (!passwordData.currentPassword.trim()) {\n            toastError('Current password is required');\n            return;\n        }\n        if (!passwordData.newPassword.trim()) {\n            toastError('New password is required');\n            return;\n        }\n        if (passwordData.newPassword !== passwordData.confirmPassword) {\n            toastError('New passwords do not match');\n            return;\n        }\n        if (passwordData.newPassword.length < 8) {\n            toastError('Password must be at least 8 characters long');\n            return;\n        }\n        if (passwordData.newPassword === passwordData.currentPassword) {\n            toastError('New password must be different from current password');\n            return;\n        }\n        setLoading(true);\n        try {\n            // First verify current password by attempting to sign in\n            const { error: signInError } = await supabase.auth.signInWithPassword({\n                email: (user === null || user === void 0 ? void 0 : user.email) || '',\n                password: passwordData.currentPassword\n            });\n            if (signInError) {\n                toastError('Current password is incorrect');\n                setLoading(false);\n                return;\n            }\n            // If current password is correct, update to new password\n            const { error: updateError } = await supabase.auth.updateUser({\n                password: passwordData.newPassword\n            });\n            if (updateError) throw updateError;\n            console.log('Password updated successfully, showing toast...');\n            success('Password updated successfully', 'Your password has been changed successfully.');\n            setPasswordData({\n                currentPassword: '',\n                newPassword: '',\n                confirmPassword: ''\n            });\n        } catch (error) {\n            console.error('Password update error:', error);\n            toastError('Failed to update password', error.message || 'Please try again');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleNotificationUpdate = async ()=>{\n        setLoading(true);\n        try {\n            // Save to local storage and user metadata\n            localStorage.setItem('notification_settings', JSON.stringify(notificationSettings));\n            // Also save to user metadata for persistence across devices\n            const { error } = await supabase.auth.updateUser({\n                data: {\n                    notification_preferences: notificationSettings\n                }\n            });\n            if (error) throw error;\n            success('Notification preferences saved successfully');\n        } catch (error) {\n            console.error('Notification update error:', error);\n            toastError('Failed to save notification preferences');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handlePasswordReset = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) {\n            toastError('No email address found');\n            return;\n        }\n        setIsResetLoading(true);\n        try {\n            const { error } = await supabase.auth.resetPasswordForEmail(user.email, {\n                redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n            });\n            if (error) throw error;\n            success('Password reset email sent!', 'Check your inbox for instructions to reset your password.');\n            setShowResetModal(false);\n        } catch (error) {\n            console.error('Password reset error:', error);\n            toastError('Failed to send reset email', error.message || 'Please try again.');\n        } finally{\n            setIsResetLoading(false);\n        }\n    };\n    const handleEmailChange = async ()=>{\n        if (!emailChangeData.newEmail.trim()) {\n            toastError('Please enter a new email address');\n            return;\n        }\n        if (!emailChangeData.currentPassword.trim()) {\n            toastError('Current password is required to change email');\n            return;\n        }\n        if (emailChangeData.newEmail === (user === null || user === void 0 ? void 0 : user.email)) {\n            toastError('New email must be different from current email');\n            return;\n        }\n        // Basic email validation\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(emailChangeData.newEmail)) {\n            toastError('Please enter a valid email address');\n            return;\n        }\n        setIsEmailLoading(true);\n        try {\n            // First verify current password\n            const { error: signInError } = await supabase.auth.signInWithPassword({\n                email: (user === null || user === void 0 ? void 0 : user.email) || '',\n                password: emailChangeData.currentPassword\n            });\n            if (signInError) {\n                toastError('Current password is incorrect');\n                setIsEmailLoading(false);\n                return;\n            }\n            // Update email\n            const { error: updateError } = await supabase.auth.updateUser({\n                email: emailChangeData.newEmail\n            });\n            if (updateError) throw updateError;\n            success('Email change initiated!', 'Check both your old and new email addresses for confirmation instructions.');\n            setShowChangeEmailModal(false);\n            setEmailChangeData({\n                newEmail: '',\n                currentPassword: ''\n            });\n        } catch (error) {\n            console.error('Email change error:', error);\n            toastError('Failed to change email', error.message || 'Please try again.');\n        } finally{\n            setIsEmailLoading(false);\n        }\n    };\n    const handleAccountDeletion = async ()=>{\n        if (!user) return;\n        setLoading(true);\n        try {\n            // Call our account deletion API\n            const response = await fetch('/api/user/delete-account', {\n                method: 'DELETE',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Failed to delete account');\n            }\n            success('Account deleted successfully. You will be redirected to the homepage.');\n            // Sign out and redirect\n            await supabase.auth.signOut();\n            setTimeout(()=>{\n                window.location.href = '/';\n            }, 2000);\n        } catch (error) {\n            console.error('Account deletion error:', error);\n            toastError('Failed to delete account', error.message || 'Please contact support.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#040716] text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-white mb-2\",\n                                    children: \"Account Settings ⚙️\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-lg\",\n                                    children: \"Manage your account settings and preferences\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 331,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b border-gray-800/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        children: sidebarItems.map((item)=>{\n                            const Icon = item.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveSection(item.id),\n                                className: \"flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors \".concat(activeSection === item.id ? 'border-orange-500 text-orange-400' : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 19\n                                    }, this),\n                                    item.label\n                                ]\n                            }, item.id, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                lineNumber: 344,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: [\n                        activeSection === 'account' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8 max-w-7xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 bg-orange-500/10 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-orange-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: \"Email address\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-300\",\n                                                            children: [\n                                                                \"Your email address is \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-white\",\n                                                                    children: user === null || user === void 0 ? void 0 : user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 74\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-400 mt-1\",\n                                                            children: \"This is used for login and important notifications\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"px-4 py-2 text-sm font-medium text-orange-400 hover:text-orange-300 hover:bg-orange-500/10 rounded-md transition-colors border border-orange-500/20 hover:border-orange-500/40\",\n                                                    onClick: ()=>setShowChangeEmailModal(true),\n                                                    children: \"Change\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 bg-blue-500/10 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: \"Account Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-medium text-white mb-1\",\n                                                                    children: \"Full Name\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300\",\n                                                                    children: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || (user === null || user === void 0 ? void 0 : (_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.first_name) + ' ' + (user === null || user === void 0 ? void 0 : (_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.last_name) || 'Not set'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-medium text-white mb-1\",\n                                                                    children: \"Account Created\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300\",\n                                                                    children: (user === null || user === void 0 ? void 0 : user.created_at) ? new Date(user.created_at).toLocaleDateString('en-US', {\n                                                                        year: 'numeric',\n                                                                        month: 'long',\n                                                                        day: 'numeric'\n                                                                    }) : 'Unknown'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-white mb-1\",\n                                                                children: \"Account Usage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-1 text-sm text-gray-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            userStats.configCount,\n                                                                            \" configurations\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            userStats.apiKeyCount,\n                                                                            \" API keys\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: [\n                                                                            userStats.userApiKeyCount,\n                                                                            \" user-generated keys\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 bg-green-500/10 rounded-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white\",\n                                                    children: \"Password\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handlePasswordChange,\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                            htmlFor: \"currentPassword\",\n                                                            className: \"text-sm font-medium text-white\",\n                                                            children: \"Current password\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    id: \"currentPassword\",\n                                                                    type: showCurrentPassword ? \"text\" : \"password\",\n                                                                    value: passwordData.currentPassword,\n                                                                    onChange: (e)=>setPasswordData((prev)=>({\n                                                                                ...prev,\n                                                                                currentPassword: e.target.value\n                                                                            })),\n                                                                    placeholder: \"••••••••••\",\n                                                                    className: \"w-full pr-10 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\",\n                                                                    required: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setShowCurrentPassword(!showCurrentPassword),\n                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300\",\n                                                                    children: showCurrentPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 50\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 89\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"newPassword\",\n                                                                    className: \"text-sm font-medium text-white\",\n                                                                    children: \"New password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"newPassword\",\n                                                                            type: showNewPassword ? \"text\" : \"password\",\n                                                                            value: passwordData.newPassword,\n                                                                            onChange: (e)=>setPasswordData((prev)=>({\n                                                                                        ...prev,\n                                                                                        newPassword: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"••••••••••\",\n                                                                            className: \"w-full pr-10 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 469,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setShowNewPassword(!showNewPassword),\n                                                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300\",\n                                                                            children: showNewPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 483,\n                                                                                columnNumber: 48\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 483,\n                                                                                columnNumber: 87\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 478,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                    htmlFor: \"confirmPassword\",\n                                                                    className: \"text-sm font-medium text-white\",\n                                                                    children: \"Confirm new password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"confirmPassword\",\n                                                                            type: showConfirmPassword ? \"text\" : \"password\",\n                                                                            value: passwordData.confirmPassword,\n                                                                            onChange: (e)=>setPasswordData((prev)=>({\n                                                                                        ...prev,\n                                                                                        confirmPassword: e.target.value\n                                                                                    })),\n                                                                            placeholder: \"••••••••••\",\n                                                                            className: \"w-full pr-10 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200\",\n                                                                            required: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 490,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300\",\n                                                                            children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 504,\n                                                                                columnNumber: 52\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 504,\n                                                                                columnNumber: 91\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 499,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-300\",\n                                                            children: [\n                                                                \"Can't remember your current password? \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    className: \"text-orange-400 hover:text-orange-300 font-medium\",\n                                                                    onClick: ()=>setShowResetModal(true),\n                                                                    children: \"Reset your password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 63\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            type: \"submit\",\n                                                            disabled: loading,\n                                                            className: \"bg-orange-600 hover:bg-orange-700 text-white\",\n                                                            children: loading ? 'Saving...' : 'Save password'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 15\n                        }, this),\n                        activeSection === 'notifications' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8 max-w-7xl mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Notification Preferences\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-white\",\n                                                                children: \"Email Notifications\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"Receive important updates via email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: notificationSettings.emailNotifications,\n                                                                onChange: (e)=>setNotificationSettings((prev)=>({\n                                                                            ...prev,\n                                                                            emailNotifications: e.target.checked\n                                                                        })),\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-white\",\n                                                                children: \"Security Alerts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"Get notified about security events\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 557,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: notificationSettings.securityAlerts,\n                                                                onChange: (e)=>setNotificationSettings((prev)=>({\n                                                                            ...prev,\n                                                                            securityAlerts: e.target.checked\n                                                                        })),\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-white\",\n                                                                children: \"Usage Alerts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"Notifications about API usage and limits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: notificationSettings.usageAlerts,\n                                                                onChange: (e)=>setNotificationSettings((prev)=>({\n                                                                            ...prev,\n                                                                            usageAlerts: e.target.checked\n                                                                        })),\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-white\",\n                                                                children: \"Marketing Emails\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"Product updates and feature announcements\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: notificationSettings.marketingEmails,\n                                                                onChange: (e)=>setNotificationSettings((prev)=>({\n                                                                            ...prev,\n                                                                            marketingEmails: e.target.checked\n                                                                        })),\n                                                                className: \"sr-only peer\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 pt-6 border-t border-gray-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: handleNotificationUpdate,\n                                            disabled: loading,\n                                            className: \"bg-orange-600 hover:bg-orange-700 text-white\",\n                                            children: loading ? 'Saving...' : 'Save Preferences'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 15\n                        }, this),\n                        activeSection === 'security' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8 max-w-7xl mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-green-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Security Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-medium text-green-400\",\n                                                                    children: \"Account Security Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 628,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-green-300\",\n                                                                    children: \"Your account is secure and protected\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 629,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800/50 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-white mb-2\",\n                                                                children: \"Last Login\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-300\",\n                                                                children: (user === null || user === void 0 ? void 0 : user.last_sign_in_at) ? new Date(user.last_sign_in_at).toLocaleString() : 'Unknown'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800/50 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-white mb-2\",\n                                                                children: \"Account Type\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-300\",\n                                                                children: \"Email & Password\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 15\n                        }, this),\n                        activeSection === 'danger' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8 max-w-7xl mx-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-900/20 backdrop-blur-sm rounded-lg border border-red-800/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-red-500/10 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white\",\n                                                children: \"Danger Zone\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-red-500/10 border border-red-500/20 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium text-red-400 mb-2\",\n                                                    children: \"⚠️ Account Deletion\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-red-300 mb-4\",\n                                                    children: \"Once you delete your account, there is no going back. This will permanently delete your account, configurations, API keys, and all associated data.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    onClick: ()=>setShowDeleteModal(true),\n                                                    className: \"bg-red-600 hover:bg-red-700 text-white\",\n                                                    children: \"Delete Account\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            showChangeEmailModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-900 border border-gray-800 rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-6 w-6 text-orange-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 686,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white\",\n                                    children: \"Change Email Address\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 685,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: \"newEmail\",\n                                            className: \"text-sm font-medium text-white\",\n                                            children: \"New Email Address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"newEmail\",\n                                            type: \"email\",\n                                            value: emailChangeData.newEmail,\n                                            onChange: (e)=>setEmailChangeData((prev)=>({\n                                                        ...prev,\n                                                        newEmail: e.target.value\n                                                    })),\n                                            placeholder: \"Enter new email address\",\n                                            className: \"w-full mt-2 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                            htmlFor: \"currentPasswordEmail\",\n                                            className: \"text-sm font-medium text-white\",\n                                            children: \"Current Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 702,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"currentPasswordEmail\",\n                                            type: \"password\",\n                                            value: emailChangeData.currentPassword,\n                                            onChange: (e)=>setEmailChangeData((prev)=>({\n                                                        ...prev,\n                                                        currentPassword: e.target.value\n                                                    })),\n                                            placeholder: \"Enter current password\",\n                                            className: \"w-full mt-2 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                            lineNumber: 703,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 701,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 689,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowChangeEmailModal(false),\n                                    className: \"flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleEmailChange,\n                                    disabled: isEmailLoading,\n                                    className: \"flex-1 bg-orange-600 hover:bg-orange-700 text-white\",\n                                    children: isEmailLoading ? 'Changing...' : 'Change Email'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 713,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                    lineNumber: 684,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                lineNumber: 683,\n                columnNumber: 9\n            }, this),\n            showResetModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-900 border border-gray-800 rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-6 w-6 text-blue-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 738,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white\",\n                                    children: \"Reset Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 739,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 737,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 mb-4\",\n                            children: \"We'll send a password reset link to your email address.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 741,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowResetModal(false),\n                                    className: \"flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handlePasswordReset,\n                                    disabled: isResetLoading,\n                                    className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white\",\n                                    children: isResetLoading ? 'Sending...' : 'Send Reset Link'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 752,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                    lineNumber: 736,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                lineNumber: 735,\n                columnNumber: 9\n            }, this),\n            showDeleteModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-900 border border-red-800/50 rounded-xl shadow-2xl max-w-md w-full p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BellIcon_EnvelopeIcon_EyeIcon_EyeSlashIcon_KeyIcon_ShieldCheckIcon_TrashIcon_UserIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-6 w-6 text-red-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-white\",\n                                    children: \"Delete Account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 770,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 768,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-300 text-sm\",\n                                children: [\n                                    \"⚠️ \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"This action cannot be undone.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                        lineNumber: 774,\n                                        columnNumber: 20\n                                    }, this),\n                                    \" This will permanently delete your account, all configurations, API keys, and associated data.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                lineNumber: 773,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 772,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 mb-6\",\n                            children: 'Are you sure you want to delete your account? Type \"DELETE\" to confirm.'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 778,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"text\",\n                            placeholder: \"Type DELETE to confirm\",\n                            className: \"w-full px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-red-500 focus:border-transparent mb-4\",\n                            onChange: (e)=>{\n                            // You can add confirmation logic here\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 781,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowDeleteModal(false),\n                                    className: \"flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 790,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleAccountDeletion,\n                                    disabled: loading,\n                                    className: \"flex-1 bg-red-600 hover:bg-red-700 text-white\",\n                                    children: loading ? 'Deleting...' : 'Delete Account'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                            lineNumber: 789,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                    lineNumber: 767,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n                lineNumber: 766,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\",\n        lineNumber: 326,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"mrO/1whaEDYY8En15/HfYhebKMM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_7__.useSubscription,\n        _components_ui_Toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGFwcCkvc2V0dGluZ3MvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDQTtBQVVQO0FBQ1c7QUFDRjtBQUNHO0FBQ21CO0FBQ1Y7QUFFM0MsU0FBU2dCO1FBbVlHQyxxQkFBa0NBLHNCQUF3Q0E7O0lBbFluRyxNQUFNQyxTQUFTaEIsMERBQVNBO0lBQ3hCLE1BQU0sRUFBRWUsSUFBSSxFQUFFLEdBQUdGLHVFQUFlQTtJQUNoQyxNQUFNSSxXQUFXTCxpRkFBMkJBO0lBQzVDLE1BQU0sRUFBRU0sT0FBTyxFQUFFQyxPQUFPQyxVQUFVLEVBQUUsR0FBR1QsOERBQVFBO0lBRS9DLE1BQU0sQ0FBQ1UsZUFBZUMsaUJBQWlCLEdBQUd4QiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUN5QixTQUFTQyxXQUFXLEdBQUcxQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUMyQixxQkFBcUJDLHVCQUF1QixHQUFHNUIsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDNkIsaUJBQWlCQyxtQkFBbUIsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQytCLHFCQUFxQkMsdUJBQXVCLEdBQUdoQywrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUNpQyxpQkFBaUJDLG1CQUFtQixHQUFHbEMsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDbUMsZ0JBQWdCQyxrQkFBa0IsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ3FDLGdCQUFnQkMsa0JBQWtCLEdBQUd0QywrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUN1QyxzQkFBc0JDLHdCQUF3QixHQUFHeEMsK0NBQVFBLENBQUM7SUFDakUsTUFBTSxDQUFDeUMsZ0JBQWdCQyxrQkFBa0IsR0FBRzFDLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQzJDLGlCQUFpQkMsbUJBQW1CLEdBQUc1QywrQ0FBUUEsQ0FBQztRQUNyRDZDLFVBQVU7UUFDVkMsaUJBQWlCO0lBQ25CO0lBRUEsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR2hELCtDQUFRQSxDQUFDO1FBQy9DOEMsaUJBQWlCO1FBQ2pCRyxhQUFhO1FBQ2JDLGlCQUFpQjtJQUNuQjtJQUVBLE1BQU0sQ0FBQ0Msc0JBQXNCQyx3QkFBd0IsR0FBR3BELCtDQUFRQSxDQUFDO1FBQy9EcUQsb0JBQW9CO1FBQ3BCQyxnQkFBZ0I7UUFDaEJDLGFBQWE7UUFDYkMsaUJBQWlCO0lBQ25CO0lBRUEsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUcxRCwrQ0FBUUEsQ0FBQztRQUN6QzJELGFBQWE7UUFDYkMsYUFBYTtRQUNiQyxpQkFBaUI7SUFDbkI7SUFFQSxNQUFNQyxlQUFlO1FBQ25CO1lBQUVDLElBQUk7WUFBV0MsT0FBTztZQUFvQkMsTUFBTTlELCtLQUFRQTtRQUFDO1FBQzNEO1lBQUU0RCxJQUFJO1lBQWlCQyxPQUFPO1lBQWlCQyxNQUFNM0QsK0tBQVFBO1FBQUM7UUFDOUQ7WUFBRXlELElBQUk7WUFBWUMsT0FBTztZQUFZQyxNQUFNNUQsZ0xBQWVBO1FBQUM7UUFDM0Q7WUFBRTBELElBQUk7WUFBVUMsT0FBTztZQUFlQyxNQUFNeEQsZ0xBQVNBO1FBQUM7S0FDdkQ7SUFFRCwyQkFBMkI7SUFDM0JSLGdEQUFTQTtrQ0FBQztZQUNSLElBQUlnQixNQUFNO2dCQUNSaUQ7Z0JBQ0FDO1lBQ0Y7UUFDRjtpQ0FBRztRQUFDbEQ7S0FBSztJQUVULE1BQU1pRCxnQkFBZ0I7UUFDcEIsSUFBSTtZQUNGLDBCQUEwQjtZQUMxQixNQUFNRSxpQkFBaUIsTUFBTUMsTUFBTTtZQUNuQyxNQUFNQyxhQUFhRixlQUFlRyxFQUFFLEdBQUcsTUFBTUgsZUFBZUksSUFBSSxLQUFLLEVBQUU7WUFDdkUsTUFBTUMsVUFBVUMsTUFBTUMsT0FBTyxDQUFDTCxjQUFjQSxhQUFhLEVBQUU7WUFFM0QsMEJBQTBCO1lBQzFCLE1BQU1NLG1CQUFtQixNQUFNUCxNQUFNO1lBQ3JDLE1BQU1RLGVBQWVELGlCQUFpQkwsRUFBRSxHQUFHLE1BQU1LLGlCQUFpQkosSUFBSSxLQUFLO2dCQUFFTSxVQUFVLEVBQUU7WUFBQztZQUMxRixNQUFNQyxXQUFXRixhQUFhQyxRQUFRLElBQUksRUFBRTtZQUU1Qyw4Q0FBOEM7WUFDOUMsSUFBSUUsZUFBZTtZQUNuQixLQUFLLE1BQU1DLFVBQVVSLFFBQVM7Z0JBQzVCLElBQUk7b0JBQ0YsTUFBTVMsZUFBZSxNQUFNYixNQUFNLHVCQUFpQyxPQUFWWSxPQUFPbEIsRUFBRSxFQUFDO29CQUNsRSxNQUFNb0IsV0FBV0QsYUFBYVgsRUFBRSxHQUFHLE1BQU1XLGFBQWFWLElBQUksS0FBSzt3QkFBRU0sVUFBVSxFQUFFO29CQUFDO29CQUM5RUUsZ0JBQWdCLENBQUNHLFNBQVNMLFFBQVEsSUFBSSxFQUFFLEVBQUVNLE1BQU07Z0JBQ2xELEVBQUUsT0FBTy9ELE9BQU87b0JBQ2RnRSxRQUFRaEUsS0FBSyxDQUFDLGtDQUFrQzRELE9BQU9sQixFQUFFLEVBQUUxQztnQkFDN0Q7WUFDRjtZQUVBcUMsYUFBYTtnQkFDWEMsYUFBYWMsUUFBUVcsTUFBTTtnQkFDM0J4QixhQUFhb0I7Z0JBQ2JuQixpQkFBaUJrQixTQUFTSyxNQUFNO1lBQ2xDO1FBQ0YsRUFBRSxPQUFPL0QsT0FBTztZQUNkZ0UsUUFBUWhFLEtBQUssQ0FBQyw2QkFBNkJBO1FBQzdDO0lBQ0Y7SUFFQSxNQUFNOEMsMkJBQTJCO1FBQy9CLElBQUk7WUFDRiwyQ0FBMkM7WUFDM0MsTUFBTW1CLGdCQUFnQkMsYUFBYUMsT0FBTyxDQUFDO1lBQzNDLElBQUlGLGVBQWU7Z0JBQ2pCbEMsd0JBQXdCcUMsS0FBS0MsS0FBSyxDQUFDSjtZQUNyQztRQUNGLEVBQUUsT0FBT2pFLE9BQU87WUFDZGdFLFFBQVFoRSxLQUFLLENBQUMsd0NBQXdDQTtRQUN4RDtJQUNGO0lBRUEsTUFBTXNFLHVCQUF1QixPQUFPQztRQUNsQ0EsRUFBRUMsY0FBYztRQUVoQixJQUFJLENBQUM5QyxhQUFhRCxlQUFlLENBQUNnRCxJQUFJLElBQUk7WUFDeEN4RSxXQUFXO1lBQ1g7UUFDRjtRQUVBLElBQUksQ0FBQ3lCLGFBQWFFLFdBQVcsQ0FBQzZDLElBQUksSUFBSTtZQUNwQ3hFLFdBQVc7WUFDWDtRQUNGO1FBRUEsSUFBSXlCLGFBQWFFLFdBQVcsS0FBS0YsYUFBYUcsZUFBZSxFQUFFO1lBQzdENUIsV0FBVztZQUNYO1FBQ0Y7UUFFQSxJQUFJeUIsYUFBYUUsV0FBVyxDQUFDbUMsTUFBTSxHQUFHLEdBQUc7WUFDdkM5RCxXQUFXO1lBQ1g7UUFDRjtRQUVBLElBQUl5QixhQUFhRSxXQUFXLEtBQUtGLGFBQWFELGVBQWUsRUFBRTtZQUM3RHhCLFdBQVc7WUFDWDtRQUNGO1FBRUFJLFdBQVc7UUFFWCxJQUFJO1lBQ0YseURBQXlEO1lBQ3pELE1BQU0sRUFBRUwsT0FBTzBFLFdBQVcsRUFBRSxHQUFHLE1BQU01RSxTQUFTNkUsSUFBSSxDQUFDQyxrQkFBa0IsQ0FBQztnQkFDcEVDLE9BQU9qRixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1pRixLQUFLLEtBQUk7Z0JBQ3RCQyxVQUFVcEQsYUFBYUQsZUFBZTtZQUN4QztZQUVBLElBQUlpRCxhQUFhO2dCQUNmekUsV0FBVztnQkFDWEksV0FBVztnQkFDWDtZQUNGO1lBRUEseURBQXlEO1lBQ3pELE1BQU0sRUFBRUwsT0FBTytFLFdBQVcsRUFBRSxHQUFHLE1BQU1qRixTQUFTNkUsSUFBSSxDQUFDSyxVQUFVLENBQUM7Z0JBQzVERixVQUFVcEQsYUFBYUUsV0FBVztZQUNwQztZQUVBLElBQUltRCxhQUFhLE1BQU1BO1lBRXZCZixRQUFRaUIsR0FBRyxDQUFDO1lBQ1psRixRQUFRLGlDQUFpQztZQUN6QzRCLGdCQUFnQjtnQkFDZEYsaUJBQWlCO2dCQUNqQkcsYUFBYTtnQkFDYkMsaUJBQWlCO1lBQ25CO1FBQ0YsRUFBRSxPQUFPN0IsT0FBWTtZQUNuQmdFLFFBQVFoRSxLQUFLLENBQUMsMEJBQTBCQTtZQUN4Q0MsV0FBVyw2QkFBNkJELE1BQU1rRixPQUFPLElBQUk7UUFDM0QsU0FBVTtZQUNSN0UsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNOEUsMkJBQTJCO1FBQy9COUUsV0FBVztRQUVYLElBQUk7WUFDRiwwQ0FBMEM7WUFDMUM2RCxhQUFha0IsT0FBTyxDQUFDLHlCQUF5QmhCLEtBQUtpQixTQUFTLENBQUN2RDtZQUU3RCw0REFBNEQ7WUFDNUQsTUFBTSxFQUFFOUIsS0FBSyxFQUFFLEdBQUcsTUFBTUYsU0FBUzZFLElBQUksQ0FBQ0ssVUFBVSxDQUFDO2dCQUMvQ00sTUFBTTtvQkFDSkMsMEJBQTBCekQ7Z0JBQzVCO1lBQ0Y7WUFFQSxJQUFJOUIsT0FBTyxNQUFNQTtZQUVqQkQsUUFBUTtRQUNWLEVBQUUsT0FBT0MsT0FBWTtZQUNuQmdFLFFBQVFoRSxLQUFLLENBQUMsOEJBQThCQTtZQUM1Q0MsV0FBVztRQUNiLFNBQVU7WUFDUkksV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNbUYsc0JBQXNCO1FBQzFCLElBQUksRUFBQzVGLGlCQUFBQSwyQkFBQUEsS0FBTWlGLEtBQUssR0FBRTtZQUNoQjVFLFdBQVc7WUFDWDtRQUNGO1FBRUFnQixrQkFBa0I7UUFDbEIsSUFBSTtZQUNGLE1BQU0sRUFBRWpCLEtBQUssRUFBRSxHQUFHLE1BQU1GLFNBQVM2RSxJQUFJLENBQUNjLHFCQUFxQixDQUFDN0YsS0FBS2lGLEtBQUssRUFBRTtnQkFDdEVhLFlBQVksR0FBMEIsT0FBdkJDLE9BQU9DLFFBQVEsQ0FBQ0MsTUFBTSxFQUFDO1lBQ3hDO1lBRUEsSUFBSTdGLE9BQU8sTUFBTUE7WUFFakJELFFBQVEsOEJBQThCO1lBQ3RDZ0Isa0JBQWtCO1FBQ3BCLEVBQUUsT0FBT2YsT0FBWTtZQUNuQmdFLFFBQVFoRSxLQUFLLENBQUMseUJBQXlCQTtZQUN2Q0MsV0FBVyw4QkFBOEJELE1BQU1rRixPQUFPLElBQUk7UUFDNUQsU0FBVTtZQUNSakUsa0JBQWtCO1FBQ3BCO0lBQ0Y7SUFFQSxNQUFNNkUsb0JBQW9CO1FBQ3hCLElBQUksQ0FBQ3hFLGdCQUFnQkUsUUFBUSxDQUFDaUQsSUFBSSxJQUFJO1lBQ3BDeEUsV0FBVztZQUNYO1FBQ0Y7UUFFQSxJQUFJLENBQUNxQixnQkFBZ0JHLGVBQWUsQ0FBQ2dELElBQUksSUFBSTtZQUMzQ3hFLFdBQVc7WUFDWDtRQUNGO1FBRUEsSUFBSXFCLGdCQUFnQkUsUUFBUSxNQUFLNUIsaUJBQUFBLDJCQUFBQSxLQUFNaUYsS0FBSyxHQUFFO1lBQzVDNUUsV0FBVztZQUNYO1FBQ0Y7UUFFQSx5QkFBeUI7UUFDekIsTUFBTThGLGFBQWE7UUFDbkIsSUFBSSxDQUFDQSxXQUFXQyxJQUFJLENBQUMxRSxnQkFBZ0JFLFFBQVEsR0FBRztZQUM5Q3ZCLFdBQVc7WUFDWDtRQUNGO1FBRUFvQixrQkFBa0I7UUFDbEIsSUFBSTtZQUNGLGdDQUFnQztZQUNoQyxNQUFNLEVBQUVyQixPQUFPMEUsV0FBVyxFQUFFLEdBQUcsTUFBTTVFLFNBQVM2RSxJQUFJLENBQUNDLGtCQUFrQixDQUFDO2dCQUNwRUMsT0FBT2pGLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWlGLEtBQUssS0FBSTtnQkFDdEJDLFVBQVV4RCxnQkFBZ0JHLGVBQWU7WUFDM0M7WUFFQSxJQUFJaUQsYUFBYTtnQkFDZnpFLFdBQVc7Z0JBQ1hvQixrQkFBa0I7Z0JBQ2xCO1lBQ0Y7WUFFQSxlQUFlO1lBQ2YsTUFBTSxFQUFFckIsT0FBTytFLFdBQVcsRUFBRSxHQUFHLE1BQU1qRixTQUFTNkUsSUFBSSxDQUFDSyxVQUFVLENBQUM7Z0JBQzVESCxPQUFPdkQsZ0JBQWdCRSxRQUFRO1lBQ2pDO1lBRUEsSUFBSXVELGFBQWEsTUFBTUE7WUFFdkJoRixRQUFRLDJCQUEyQjtZQUNuQ29CLHdCQUF3QjtZQUN4QkksbUJBQW1CO2dCQUFFQyxVQUFVO2dCQUFJQyxpQkFBaUI7WUFBRztRQUN6RCxFQUFFLE9BQU96QixPQUFZO1lBQ25CZ0UsUUFBUWhFLEtBQUssQ0FBQyx1QkFBdUJBO1lBQ3JDQyxXQUFXLDBCQUEwQkQsTUFBTWtGLE9BQU8sSUFBSTtRQUN4RCxTQUFVO1lBQ1I3RCxrQkFBa0I7UUFDcEI7SUFDRjtJQUVBLE1BQU00RSx3QkFBd0I7UUFDNUIsSUFBSSxDQUFDckcsTUFBTTtRQUVYUyxXQUFXO1FBQ1gsSUFBSTtZQUNGLGdDQUFnQztZQUNoQyxNQUFNNkYsV0FBVyxNQUFNbEQsTUFBTSw0QkFBNEI7Z0JBQ3ZEbUQsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7WUFFQSxJQUFJLENBQUNGLFNBQVNoRCxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU1tRCxZQUFZLE1BQU1ILFNBQVMvQyxJQUFJO2dCQUNyQyxNQUFNLElBQUltRCxNQUFNRCxVQUFVckcsS0FBSyxJQUFJO1lBQ3JDO1lBRUFELFFBQVE7WUFFUix3QkFBd0I7WUFDeEIsTUFBTUQsU0FBUzZFLElBQUksQ0FBQzRCLE9BQU87WUFDM0JDLFdBQVc7Z0JBQ1RiLE9BQU9DLFFBQVEsQ0FBQ2EsSUFBSSxHQUFHO1lBQ3pCLEdBQUc7UUFFTCxFQUFFLE9BQU96RyxPQUFZO1lBQ25CZ0UsUUFBUWhFLEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDQyxXQUFXLDRCQUE0QkQsTUFBTWtGLE9BQU8sSUFBSTtRQUMxRCxTQUFVO1lBQ1I3RSxXQUFXO1FBQ2I7SUFDRjtJQUVBLHFCQUNFLDhEQUFDcUc7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzs4Q0FDQyw4REFBQ0U7b0NBQUdELFdBQVU7OENBQXFDOzs7Ozs7OENBR25ELDhEQUFDRTtvQ0FBRUYsV0FBVTs4Q0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTN0MsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0c7d0JBQUlILFdBQVU7a0NBQ1psRSxhQUFhc0UsR0FBRyxDQUFDLENBQUNDOzRCQUNqQixNQUFNQyxPQUFPRCxLQUFLcEUsSUFBSTs0QkFDdEIscUJBQ0UsOERBQUNzRTtnQ0FFQ0MsU0FBUyxJQUFNaEgsaUJBQWlCNkcsS0FBS3RFLEVBQUU7Z0NBQ3ZDaUUsV0FBVyxzRkFJVixPQUhDekcsa0JBQWtCOEcsS0FBS3RFLEVBQUUsR0FDckIsc0NBQ0E7O2tEQUdOLDhEQUFDdUU7d0NBQUtOLFdBQVU7Ozs7OztvQ0FDZkssS0FBS3JFLEtBQUs7OytCQVROcUUsS0FBS3RFLEVBQUU7Ozs7O3dCQVlsQjs7Ozs7Ozs7Ozs7Ozs7OzswQkFNTiw4REFBQ2dFO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7d0JBQ1Z6RyxrQkFBa0IsMkJBQ2pCLDhEQUFDd0c7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUN0SCxnTEFBWUE7d0RBQUNzSCxXQUFVOzs7Ozs7Ozs7Ozs4REFFMUIsOERBQUNTO29EQUFHVCxXQUFVOzhEQUFtQzs7Ozs7Ozs7Ozs7O3NEQUVuRCw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs7c0VBQ0MsOERBQUNHOzREQUFFRixXQUFVOztnRUFBZ0I7OEVBQXNCLDhEQUFDVTtvRUFBS1YsV0FBVTs4RUFBMEIvRyxpQkFBQUEsMkJBQUFBLEtBQU1pRixLQUFLOzs7Ozs7Ozs7Ozs7c0VBQ3hHLDhEQUFDZ0M7NERBQUVGLFdBQVU7c0VBQTZCOzs7Ozs7Ozs7Ozs7OERBRTVDLDhEQUFDTztvREFDQ1AsV0FBVTtvREFDVlEsU0FBUyxJQUFNaEcsd0JBQXdCOzhEQUN4Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU9MLDhEQUFDdUY7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDN0gsK0tBQVFBO3dEQUFDNkgsV0FBVTs7Ozs7Ozs7Ozs7OERBRXRCLDhEQUFDUztvREFBR1QsV0FBVTs4REFBbUM7Ozs7Ozs7Ozs7OztzREFFbkQsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDRDs7OEVBQ0MsOERBQUNZO29FQUFHWCxXQUFVOzhFQUFzQzs7Ozs7OzhFQUNwRCw4REFBQ0U7b0VBQUVGLFdBQVU7OEVBQ1YvRyxDQUFBQSxpQkFBQUEsNEJBQUFBLHNCQUFBQSxLQUFNMkgsYUFBYSxjQUFuQjNILDBDQUFBQSxvQkFBcUI0SCxTQUFTLEtBQUk1SCxDQUFBQSxpQkFBQUEsNEJBQUFBLHVCQUFBQSxLQUFNMkgsYUFBYSxjQUFuQjNILDJDQUFBQSxxQkFBcUI2SCxVQUFVLElBQUcsT0FBTTdILGlCQUFBQSw0QkFBQUEsdUJBQUFBLEtBQU0ySCxhQUFhLGNBQW5CM0gsMkNBQUFBLHFCQUFxQjhILFNBQVMsS0FBSTs7Ozs7Ozs7Ozs7O3NFQUdqSCw4REFBQ2hCOzs4RUFDQyw4REFBQ1k7b0VBQUdYLFdBQVU7OEVBQXNDOzs7Ozs7OEVBQ3BELDhEQUFDRTtvRUFBRUYsV0FBVTs4RUFDVi9HLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTStILFVBQVUsSUFBRyxJQUFJQyxLQUFLaEksS0FBSytILFVBQVUsRUFBRUUsa0JBQWtCLENBQUMsU0FBUzt3RUFDeEVDLE1BQU07d0VBQ05DLE9BQU87d0VBQ1BDLEtBQUs7b0VBQ1AsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUlYLDhEQUFDdEI7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNEOzswRUFDQyw4REFBQ1k7Z0VBQUdYLFdBQVU7MEVBQXNDOzs7Ozs7MEVBQ3BELDhEQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNFOzs0RUFBR3pFLFVBQVVFLFdBQVc7NEVBQUM7Ozs7Ozs7a0ZBQzFCLDhEQUFDdUU7OzRFQUFHekUsVUFBVUcsV0FBVzs0RUFBQzs7Ozs7OztrRkFDMUIsOERBQUNzRTs7NEVBQUd6RSxVQUFVSSxlQUFlOzRFQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBUXhDLDhEQUFDa0U7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOzhEQUNiLDRFQUFDNUgsZ0xBQU9BO3dEQUFDNEgsV0FBVTs7Ozs7Ozs7Ozs7OERBRXJCLDhEQUFDUztvREFBR1QsV0FBVTs4REFBbUM7Ozs7Ozs7Ozs7OztzREFFbkQsOERBQUNzQjs0Q0FBS0MsVUFBVTVEOzRDQUFzQnFDLFdBQVU7OzhEQUM5Qyw4REFBQ0Q7O3NFQUNDLDhEQUFDbkgsdURBQUtBOzREQUFDNEksU0FBUTs0REFBa0J4QixXQUFVO3NFQUFpQzs7Ozs7O3NFQUM1RSw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDeUI7b0VBQ0MxRixJQUFHO29FQUNIMkYsTUFBTS9ILHNCQUFzQixTQUFTO29FQUNyQ2dJLE9BQU81RyxhQUFhRCxlQUFlO29FQUNuQzhHLFVBQVUsQ0FBQ2hFLElBQU01QyxnQkFBZ0I2RyxDQUFBQSxPQUFTO2dGQUFFLEdBQUdBLElBQUk7Z0ZBQUUvRyxpQkFBaUI4QyxFQUFFa0UsTUFBTSxDQUFDSCxLQUFLOzRFQUFDO29FQUNyRkksYUFBWTtvRUFDWi9CLFdBQVU7b0VBQ1ZnQyxRQUFROzs7Ozs7OEVBRVYsOERBQUN6QjtvRUFDQ21CLE1BQUs7b0VBQ0xsQixTQUFTLElBQU01Ryx1QkFBdUIsQ0FBQ0Q7b0VBQ3ZDcUcsV0FBVTs4RUFFVHJHLG9DQUFzQiw4REFBQ25CLGdMQUFZQTt3RUFBQ3dILFdBQVU7Ozs7OzZGQUFlLDhEQUFDekgsZ0xBQU9BO3dFQUFDeUgsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSXZGLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzs4RUFDQyw4REFBQ25ILHVEQUFLQTtvRUFBQzRJLFNBQVE7b0VBQWN4QixXQUFVOzhFQUFpQzs7Ozs7OzhFQUN4RSw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDeUI7NEVBQ0MxRixJQUFHOzRFQUNIMkYsTUFBTTdILGtCQUFrQixTQUFTOzRFQUNqQzhILE9BQU81RyxhQUFhRSxXQUFXOzRFQUMvQjJHLFVBQVUsQ0FBQ2hFLElBQU01QyxnQkFBZ0I2RyxDQUFBQSxPQUFTO3dGQUFFLEdBQUdBLElBQUk7d0ZBQUU1RyxhQUFhMkMsRUFBRWtFLE1BQU0sQ0FBQ0gsS0FBSztvRkFBQzs0RUFDakZJLGFBQVk7NEVBQ1ovQixXQUFVOzRFQUNWZ0MsUUFBUTs7Ozs7O3NGQUVWLDhEQUFDekI7NEVBQ0NtQixNQUFLOzRFQUNMbEIsU0FBUyxJQUFNMUcsbUJBQW1CLENBQUNEOzRFQUNuQ21HLFdBQVU7c0ZBRVRuRyxnQ0FBa0IsOERBQUNyQixnTEFBWUE7Z0ZBQUN3SCxXQUFVOzs7OztxR0FBZSw4REFBQ3pILGdMQUFPQTtnRkFBQ3lILFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQUluRiw4REFBQ0Q7OzhFQUNDLDhEQUFDbkgsdURBQUtBO29FQUFDNEksU0FBUTtvRUFBa0J4QixXQUFVOzhFQUFpQzs7Ozs7OzhFQUM1RSw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDeUI7NEVBQ0MxRixJQUFHOzRFQUNIMkYsTUFBTTNILHNCQUFzQixTQUFTOzRFQUNyQzRILE9BQU81RyxhQUFhRyxlQUFlOzRFQUNuQzBHLFVBQVUsQ0FBQ2hFLElBQU01QyxnQkFBZ0I2RyxDQUFBQSxPQUFTO3dGQUFFLEdBQUdBLElBQUk7d0ZBQUUzRyxpQkFBaUIwQyxFQUFFa0UsTUFBTSxDQUFDSCxLQUFLO29GQUFDOzRFQUNyRkksYUFBWTs0RUFDWi9CLFdBQVU7NEVBQ1ZnQyxRQUFROzs7Ozs7c0ZBRVYsOERBQUN6Qjs0RUFDQ21CLE1BQUs7NEVBQ0xsQixTQUFTLElBQU14Ryx1QkFBdUIsQ0FBQ0Q7NEVBQ3ZDaUcsV0FBVTtzRkFFVGpHLG9DQUFzQiw4REFBQ3ZCLGdMQUFZQTtnRkFBQ3dILFdBQVU7Ozs7O3FHQUFlLDhEQUFDekgsZ0xBQU9BO2dGQUFDeUgsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBS3pGLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOztnRUFBd0I7OEVBQ0MsOERBQUNPO29FQUNyQ21CLE1BQUs7b0VBQ0wxQixXQUFVO29FQUNWUSxTQUFTLElBQU1wRyxrQkFBa0I7OEVBQ2xDOzs7Ozs7Ozs7Ozs7c0VBSUgsOERBQUN6Qix5REFBTUE7NERBQUMrSSxNQUFLOzREQUFTTyxVQUFVeEk7NERBQVN1RyxXQUFVO3NFQUNoRHZHLFVBQVUsY0FBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQVFwQ0Ysa0JBQWtCLGlDQUNqQiw4REFBQ3dHOzRCQUFJQyxXQUFVO3NDQUViLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUMxSCwrS0FBUUE7b0RBQUMwSCxXQUFVOzs7Ozs7Ozs7OzswREFFdEIsOERBQUNTO2dEQUFHVCxXQUFVOzBEQUFtQzs7Ozs7Ozs7Ozs7O2tEQUVuRCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEOzswRUFDQyw4REFBQ1k7Z0VBQUdYLFdBQVU7MEVBQWlDOzs7Ozs7MEVBQy9DLDhEQUFDRTtnRUFBRUYsV0FBVTswRUFBd0I7Ozs7Ozs7Ozs7OztrRUFFdkMsOERBQUNoRTt3REFBTWdFLFdBQVU7OzBFQUNmLDhEQUFDeUI7Z0VBQ0NDLE1BQUs7Z0VBQ0xRLFNBQVMvRyxxQkFBcUJFLGtCQUFrQjtnRUFDaER1RyxVQUFVLENBQUNoRSxJQUFNeEMsd0JBQXdCeUcsQ0FBQUEsT0FBUzs0RUFBRSxHQUFHQSxJQUFJOzRFQUFFeEcsb0JBQW9CdUMsRUFBRWtFLE1BQU0sQ0FBQ0ksT0FBTzt3RUFBQztnRUFDbEdsQyxXQUFVOzs7Ozs7MEVBRVosOERBQUNEO2dFQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBR25CLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEOzswRUFDQyw4REFBQ1k7Z0VBQUdYLFdBQVU7MEVBQWlDOzs7Ozs7MEVBQy9DLDhEQUFDRTtnRUFBRUYsV0FBVTswRUFBd0I7Ozs7Ozs7Ozs7OztrRUFFdkMsOERBQUNoRTt3REFBTWdFLFdBQVU7OzBFQUNmLDhEQUFDeUI7Z0VBQ0NDLE1BQUs7Z0VBQ0xRLFNBQVMvRyxxQkFBcUJHLGNBQWM7Z0VBQzVDc0csVUFBVSxDQUFDaEUsSUFBTXhDLHdCQUF3QnlHLENBQUFBLE9BQVM7NEVBQUUsR0FBR0EsSUFBSTs0RUFBRXZHLGdCQUFnQnNDLEVBQUVrRSxNQUFNLENBQUNJLE9BQU87d0VBQUM7Z0VBQzlGbEMsV0FBVTs7Ozs7OzBFQUVaLDhEQUFDRDtnRUFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQUduQiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDs7MEVBQ0MsOERBQUNZO2dFQUFHWCxXQUFVOzBFQUFpQzs7Ozs7OzBFQUMvQyw4REFBQ0U7Z0VBQUVGLFdBQVU7MEVBQXdCOzs7Ozs7Ozs7Ozs7a0VBRXZDLDhEQUFDaEU7d0RBQU1nRSxXQUFVOzswRUFDZiw4REFBQ3lCO2dFQUNDQyxNQUFLO2dFQUNMUSxTQUFTL0cscUJBQXFCSSxXQUFXO2dFQUN6Q3FHLFVBQVUsQ0FBQ2hFLElBQU14Qyx3QkFBd0J5RyxDQUFBQSxPQUFTOzRFQUFFLEdBQUdBLElBQUk7NEVBQUV0RyxhQUFhcUMsRUFBRWtFLE1BQU0sQ0FBQ0ksT0FBTzt3RUFBQztnRUFDM0ZsQyxXQUFVOzs7Ozs7MEVBRVosOERBQUNEO2dFQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBR25CLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEOzswRUFDQyw4REFBQ1k7Z0VBQUdYLFdBQVU7MEVBQWlDOzs7Ozs7MEVBQy9DLDhEQUFDRTtnRUFBRUYsV0FBVTswRUFBd0I7Ozs7Ozs7Ozs7OztrRUFFdkMsOERBQUNoRTt3REFBTWdFLFdBQVU7OzBFQUNmLDhEQUFDeUI7Z0VBQ0NDLE1BQUs7Z0VBQ0xRLFNBQVMvRyxxQkFBcUJLLGVBQWU7Z0VBQzdDb0csVUFBVSxDQUFDaEUsSUFBTXhDLHdCQUF3QnlHLENBQUFBLE9BQVM7NEVBQUUsR0FBR0EsSUFBSTs0RUFBRXJHLGlCQUFpQm9DLEVBQUVrRSxNQUFNLENBQUNJLE9BQU87d0VBQUM7Z0VBQy9GbEMsV0FBVTs7Ozs7OzBFQUVaLDhEQUFDRDtnRUFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUlyQiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNySCx5REFBTUE7NENBQ0w2SCxTQUFTaEM7NENBQ1R5RCxVQUFVeEk7NENBQ1Z1RyxXQUFVO3NEQUVUdkcsVUFBVSxjQUFjOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQU9sQ0Ysa0JBQWtCLDRCQUNqQiw4REFBQ3dHOzRCQUFJQyxXQUFVO3NDQUViLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUMzSCxnTEFBZUE7b0RBQUMySCxXQUFVOzs7Ozs7Ozs7OzswREFFN0IsOERBQUNTO2dEQUFHVCxXQUFVOzBEQUFtQzs7Ozs7Ozs7Ozs7O2tEQUVuRCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDM0gsZ0xBQWVBOzREQUFDMkgsV0FBVTs7Ozs7O3NFQUMzQiw4REFBQ0Q7OzhFQUNDLDhEQUFDWTtvRUFBR1gsV0FBVTs4RUFBcUM7Ozs7Ozs4RUFDbkQsOERBQUNFO29FQUFFRixXQUFVOzhFQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSTVDLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ1c7Z0VBQUdYLFdBQVU7MEVBQXNDOzs7Ozs7MEVBQ3BELDhEQUFDRTtnRUFBRUYsV0FBVTswRUFDVi9HLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWtKLGVBQWUsSUFBRyxJQUFJbEIsS0FBS2hJLEtBQUtrSixlQUFlLEVBQUVDLGNBQWMsS0FBSzs7Ozs7Ozs7Ozs7O2tFQUcvRSw4REFBQ3JDO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ1c7Z0VBQUdYLFdBQVU7MEVBQXNDOzs7Ozs7MEVBQ3BELDhEQUFDRTtnRUFBRUYsV0FBVTswRUFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQVFoRHpHLGtCQUFrQiwwQkFDakIsOERBQUN3Rzs0QkFBSUMsV0FBVTtzQ0FFYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDdkgsZ0xBQVNBO29EQUFDdUgsV0FBVTs7Ozs7Ozs7Ozs7MERBRXZCLDhEQUFDUztnREFBR1QsV0FBVTswREFBbUM7Ozs7Ozs7Ozs7OztrREFFbkQsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNXO29EQUFHWCxXQUFVOzhEQUF3Qzs7Ozs7OzhEQUN0RCw4REFBQ0U7b0RBQUVGLFdBQVU7OERBQTRCOzs7Ozs7OERBSXpDLDhEQUFDckgseURBQU1BO29EQUNMNkgsU0FBUyxJQUFNdEcsbUJBQW1CO29EQUNsQzhGLFdBQVU7OERBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVloQnpGLHNDQUNDLDhEQUFDd0Y7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDdEgsZ0xBQVlBO29DQUFDc0gsV0FBVTs7Ozs7OzhDQUN4Qiw4REFBQ1M7b0NBQUdULFdBQVU7OENBQW1DOzs7Ozs7Ozs7Ozs7c0NBRW5ELDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEOztzREFDQyw4REFBQ25ILHVEQUFLQTs0Q0FBQzRJLFNBQVE7NENBQVd4QixXQUFVO3NEQUFpQzs7Ozs7O3NEQUNyRSw4REFBQ3lCOzRDQUNDMUYsSUFBRzs0Q0FDSDJGLE1BQUs7NENBQ0xDLE9BQU9oSCxnQkFBZ0JFLFFBQVE7NENBQy9CK0csVUFBVSxDQUFDaEUsSUFBTWhELG1CQUFtQmlILENBQUFBLE9BQVM7d0RBQUUsR0FBR0EsSUFBSTt3REFBRWhILFVBQVUrQyxFQUFFa0UsTUFBTSxDQUFDSCxLQUFLO29EQUFDOzRDQUNqRkksYUFBWTs0Q0FDWi9CLFdBQVU7Ozs7Ozs7Ozs7Ozs4Q0FHZCw4REFBQ0Q7O3NEQUNDLDhEQUFDbkgsdURBQUtBOzRDQUFDNEksU0FBUTs0Q0FBdUJ4QixXQUFVO3NEQUFpQzs7Ozs7O3NEQUNqRiw4REFBQ3lCOzRDQUNDMUYsSUFBRzs0Q0FDSDJGLE1BQUs7NENBQ0xDLE9BQU9oSCxnQkFBZ0JHLGVBQWU7NENBQ3RDOEcsVUFBVSxDQUFDaEUsSUFBTWhELG1CQUFtQmlILENBQUFBLE9BQVM7d0RBQUUsR0FBR0EsSUFBSTt3REFBRS9HLGlCQUFpQjhDLEVBQUVrRSxNQUFNLENBQUNILEtBQUs7b0RBQUM7NENBQ3hGSSxhQUFZOzRDQUNaL0IsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUloQiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDckgseURBQU1BO29DQUNMMEosU0FBUTtvQ0FDUjdCLFNBQVMsSUFBTWhHLHdCQUF3QjtvQ0FDdkN3RixXQUFVOzhDQUNYOzs7Ozs7OENBR0QsOERBQUNySCx5REFBTUE7b0NBQ0w2SCxTQUFTckI7b0NBQ1Q4QyxVQUFVeEg7b0NBQ1Z1RixXQUFVOzhDQUVUdkYsaUJBQWlCLGdCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFRM0NOLGdDQUNDLDhEQUFDNEY7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDNUgsZ0xBQU9BO29DQUFDNEgsV0FBVTs7Ozs7OzhDQUNuQiw4REFBQ1M7b0NBQUdULFdBQVU7OENBQW1DOzs7Ozs7Ozs7Ozs7c0NBRW5ELDhEQUFDRTs0QkFBRUYsV0FBVTtzQ0FBcUI7Ozs7OztzQ0FHbEMsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3JILHlEQUFNQTtvQ0FDTDBKLFNBQVE7b0NBQ1I3QixTQUFTLElBQU1wRyxrQkFBa0I7b0NBQ2pDNEYsV0FBVTs4Q0FDWDs7Ozs7OzhDQUdELDhEQUFDckgseURBQU1BO29DQUNMNkgsU0FBUzNCO29DQUNUb0QsVUFBVTVIO29DQUNWMkYsV0FBVTs4Q0FFVDNGLGlCQUFpQixlQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQVExQ0osaUNBQ0MsOERBQUM4RjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUN2SCxnTEFBU0E7b0NBQUN1SCxXQUFVOzs7Ozs7OENBQ3JCLDhEQUFDUztvQ0FBR1QsV0FBVTs4Q0FBbUM7Ozs7Ozs7Ozs7OztzQ0FFbkQsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRTtnQ0FBRUYsV0FBVTs7b0NBQXVCO2tEQUMvQiw4REFBQ3NDO2tEQUFPOzs7Ozs7b0NBQXNDOzs7Ozs7Ozs7Ozs7c0NBSXJELDhEQUFDcEM7NEJBQUVGLFdBQVU7c0NBQXFCOzs7Ozs7c0NBR2xDLDhEQUFDeUI7NEJBQ0NDLE1BQUs7NEJBQ0xLLGFBQVk7NEJBQ1ovQixXQUFVOzRCQUNWNEIsVUFBVSxDQUFDaEU7NEJBQ1Qsc0NBQXNDOzRCQUN4Qzs7Ozs7O3NDQUVGLDhEQUFDbUM7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDckgseURBQU1BO29DQUNMMEosU0FBUTtvQ0FDUjdCLFNBQVMsSUFBTXRHLG1CQUFtQjtvQ0FDbEM4RixXQUFVOzhDQUNYOzs7Ozs7OENBR0QsOERBQUNySCx5REFBTUE7b0NBQ0w2SCxTQUFTbEI7b0NBQ1QyQyxVQUFVeEk7b0NBQ1Z1RyxXQUFVOzhDQUVUdkcsVUFBVSxnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUTNDO0dBcnhCd0JUOztRQUNQZCxzREFBU0E7UUFDUGEsbUVBQWVBO1FBRU9GLDBEQUFRQTs7O0tBSnpCRyIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGFwcFxcKGFwcClcXHNldHRpbmdzXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuaW1wb3J0IHtcbiAgVXNlckljb24sXG4gIEtleUljb24sXG4gIFNoaWVsZENoZWNrSWNvbixcbiAgQmVsbEljb24sXG4gIEV5ZUljb24sXG4gIEV5ZVNsYXNoSWNvbixcbiAgVHJhc2hJY29uLFxuICBFbnZlbG9wZUljb25cbn0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9CdXR0b24nO1xuaW1wb3J0IHsgTGFiZWwgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvbGFiZWwnO1xuaW1wb3J0IHsgdXNlVG9hc3QgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvVG9hc3QnO1xuaW1wb3J0IHsgY3JlYXRlU3VwYWJhc2VCcm93c2VyQ2xpZW50IH0gZnJvbSAnQC9saWIvc3VwYWJhc2UvY2xpZW50JztcbmltcG9ydCB7IHVzZVN1YnNjcmlwdGlvbiB9IGZyb20gJ0AvaG9va3MvdXNlU3Vic2NyaXB0aW9uJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2V0dGluZ3NQYWdlKCkge1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VTdWJzY3JpcHRpb24oKTtcbiAgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVTdXBhYmFzZUJyb3dzZXJDbGllbnQoKTtcbiAgY29uc3QgeyBzdWNjZXNzLCBlcnJvcjogdG9hc3RFcnJvciB9ID0gdXNlVG9hc3QoKTtcblxuICBjb25zdCBbYWN0aXZlU2VjdGlvbiwgc2V0QWN0aXZlU2VjdGlvbl0gPSB1c2VTdGF0ZSgnYWNjb3VudCcpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93Q3VycmVudFBhc3N3b3JkLCBzZXRTaG93Q3VycmVudFBhc3N3b3JkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dOZXdQYXNzd29yZCwgc2V0U2hvd05ld1Bhc3N3b3JkXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dDb25maXJtUGFzc3dvcmQsIHNldFNob3dDb25maXJtUGFzc3dvcmRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd0RlbGV0ZU1vZGFsLCBzZXRTaG93RGVsZXRlTW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd1Jlc2V0TW9kYWwsIHNldFNob3dSZXNldE1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzUmVzZXRMb2FkaW5nLCBzZXRJc1Jlc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtzaG93Q2hhbmdlRW1haWxNb2RhbCwgc2V0U2hvd0NoYW5nZUVtYWlsTW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNFbWFpbExvYWRpbmcsIHNldElzRW1haWxMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2VtYWlsQ2hhbmdlRGF0YSwgc2V0RW1haWxDaGFuZ2VEYXRhXSA9IHVzZVN0YXRlKHtcbiAgICBuZXdFbWFpbDogJycsXG4gICAgY3VycmVudFBhc3N3b3JkOiAnJ1xuICB9KTtcblxuICBjb25zdCBbcGFzc3dvcmREYXRhLCBzZXRQYXNzd29yZERhdGFdID0gdXNlU3RhdGUoe1xuICAgIGN1cnJlbnRQYXNzd29yZDogJycsXG4gICAgbmV3UGFzc3dvcmQ6ICcnLFxuICAgIGNvbmZpcm1QYXNzd29yZDogJydcbiAgfSk7XG5cbiAgY29uc3QgW25vdGlmaWNhdGlvblNldHRpbmdzLCBzZXROb3RpZmljYXRpb25TZXR0aW5nc10gPSB1c2VTdGF0ZSh7XG4gICAgZW1haWxOb3RpZmljYXRpb25zOiB0cnVlLFxuICAgIHNlY3VyaXR5QWxlcnRzOiB0cnVlLFxuICAgIHVzYWdlQWxlcnRzOiB0cnVlLFxuICAgIG1hcmtldGluZ0VtYWlsczogZmFsc2VcbiAgfSk7XG5cbiAgY29uc3QgW3VzZXJTdGF0cywgc2V0VXNlclN0YXRzXSA9IHVzZVN0YXRlKHtcbiAgICBjb25maWdDb3VudDogMCxcbiAgICBhcGlLZXlDb3VudDogMCxcbiAgICB1c2VyQXBpS2V5Q291bnQ6IDBcbiAgfSk7XG5cbiAgY29uc3Qgc2lkZWJhckl0ZW1zID0gW1xuICAgIHsgaWQ6ICdhY2NvdW50JywgbGFiZWw6ICdBY2NvdW50IHNldHRpbmdzJywgaWNvbjogVXNlckljb24gfSxcbiAgICB7IGlkOiAnbm90aWZpY2F0aW9ucycsIGxhYmVsOiAnTm90aWZpY2F0aW9ucycsIGljb246IEJlbGxJY29uIH0sXG4gICAgeyBpZDogJ3NlY3VyaXR5JywgbGFiZWw6ICdTZWN1cml0eScsIGljb246IFNoaWVsZENoZWNrSWNvbiB9LFxuICAgIHsgaWQ6ICdkYW5nZXInLCBsYWJlbDogJ0RhbmdlciB6b25lJywgaWNvbjogVHJhc2hJY29uIH1cbiAgXTtcblxuICAvLyBMb2FkIHVzZXIgZGF0YSBhbmQgc3RhdHNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodXNlcikge1xuICAgICAgbG9hZFVzZXJTdGF0cygpO1xuICAgICAgbG9hZE5vdGlmaWNhdGlvblNldHRpbmdzKCk7XG4gICAgfVxuICB9LCBbdXNlcl0pO1xuXG4gIGNvbnN0IGxvYWRVc2VyU3RhdHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEdldCBjb25maWd1cmF0aW9uIGNvdW50XG4gICAgICBjb25zdCBjb25maWdSZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2N1c3RvbS1jb25maWdzJyk7XG4gICAgICBjb25zdCBjb25maWdEYXRhID0gY29uZmlnUmVzcG9uc2Uub2sgPyBhd2FpdCBjb25maWdSZXNwb25zZS5qc29uKCkgOiBbXTtcbiAgICAgIGNvbnN0IGNvbmZpZ3MgPSBBcnJheS5pc0FycmF5KGNvbmZpZ0RhdGEpID8gY29uZmlnRGF0YSA6IFtdO1xuXG4gICAgICAvLyBHZXQgdXNlciBBUEkga2V5cyBjb3VudFxuICAgICAgY29uc3QgdXNlcktleXNSZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3VzZXItYXBpLWtleXMnKTtcbiAgICAgIGNvbnN0IHVzZXJLZXlzRGF0YSA9IHVzZXJLZXlzUmVzcG9uc2Uub2sgPyBhd2FpdCB1c2VyS2V5c1Jlc3BvbnNlLmpzb24oKSA6IHsgYXBpX2tleXM6IFtdIH07XG4gICAgICBjb25zdCB1c2VyS2V5cyA9IHVzZXJLZXlzRGF0YS5hcGlfa2V5cyB8fCBbXTtcblxuICAgICAgLy8gR2V0IHRvdGFsIEFQSSBrZXlzIGNvdW50IGFjcm9zcyBhbGwgY29uZmlnc1xuICAgICAgbGV0IHRvdGFsQXBpS2V5cyA9IDA7XG4gICAgICBmb3IgKGNvbnN0IGNvbmZpZyBvZiBjb25maWdzKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3Qga2V5c1Jlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvY3VzdG9tLWNvbmZpZ3MvJHtjb25maWcuaWR9L2tleXNgKTtcbiAgICAgICAgICBjb25zdCBrZXlzRGF0YSA9IGtleXNSZXNwb25zZS5vayA/IGF3YWl0IGtleXNSZXNwb25zZS5qc29uKCkgOiB7IGFwaV9rZXlzOiBbXSB9O1xuICAgICAgICAgIHRvdGFsQXBpS2V5cyArPSAoa2V5c0RhdGEuYXBpX2tleXMgfHwgW10pLmxlbmd0aDtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGtleXMgZm9yIGNvbmZpZzonLCBjb25maWcuaWQsIGVycm9yKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBzZXRVc2VyU3RhdHMoe1xuICAgICAgICBjb25maWdDb3VudDogY29uZmlncy5sZW5ndGgsXG4gICAgICAgIGFwaUtleUNvdW50OiB0b3RhbEFwaUtleXMsXG4gICAgICAgIHVzZXJBcGlLZXlDb3VudDogdXNlcktleXMubGVuZ3RoXG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyB1c2VyIHN0YXRzOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgbG9hZE5vdGlmaWNhdGlvblNldHRpbmdzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBMb2FkIGZyb20gdXNlciBtZXRhZGF0YSBvciBsb2NhbCBzdG9yYWdlXG4gICAgICBjb25zdCBzYXZlZFNldHRpbmdzID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ25vdGlmaWNhdGlvbl9zZXR0aW5ncycpO1xuICAgICAgaWYgKHNhdmVkU2V0dGluZ3MpIHtcbiAgICAgICAgc2V0Tm90aWZpY2F0aW9uU2V0dGluZ3MoSlNPTi5wYXJzZShzYXZlZFNldHRpbmdzKSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgbm90aWZpY2F0aW9uIHNldHRpbmdzOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUGFzc3dvcmRDaGFuZ2UgPSBhc3luYyAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuXG4gICAgaWYgKCFwYXNzd29yZERhdGEuY3VycmVudFBhc3N3b3JkLnRyaW0oKSkge1xuICAgICAgdG9hc3RFcnJvcignQ3VycmVudCBwYXNzd29yZCBpcyByZXF1aXJlZCcpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGlmICghcGFzc3dvcmREYXRhLm5ld1Bhc3N3b3JkLnRyaW0oKSkge1xuICAgICAgdG9hc3RFcnJvcignTmV3IHBhc3N3b3JkIGlzIHJlcXVpcmVkJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgaWYgKHBhc3N3b3JkRGF0YS5uZXdQYXNzd29yZCAhPT0gcGFzc3dvcmREYXRhLmNvbmZpcm1QYXNzd29yZCkge1xuICAgICAgdG9hc3RFcnJvcignTmV3IHBhc3N3b3JkcyBkbyBub3QgbWF0Y2gnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAocGFzc3dvcmREYXRhLm5ld1Bhc3N3b3JkLmxlbmd0aCA8IDgpIHtcbiAgICAgIHRvYXN0RXJyb3IoJ1Bhc3N3b3JkIG11c3QgYmUgYXQgbGVhc3QgOCBjaGFyYWN0ZXJzIGxvbmcnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAocGFzc3dvcmREYXRhLm5ld1Bhc3N3b3JkID09PSBwYXNzd29yZERhdGEuY3VycmVudFBhc3N3b3JkKSB7XG4gICAgICB0b2FzdEVycm9yKCdOZXcgcGFzc3dvcmQgbXVzdCBiZSBkaWZmZXJlbnQgZnJvbSBjdXJyZW50IHBhc3N3b3JkJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0TG9hZGluZyh0cnVlKTtcblxuICAgIHRyeSB7XG4gICAgICAvLyBGaXJzdCB2ZXJpZnkgY3VycmVudCBwYXNzd29yZCBieSBhdHRlbXB0aW5nIHRvIHNpZ24gaW5cbiAgICAgIGNvbnN0IHsgZXJyb3I6IHNpZ25JbkVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnNpZ25JbldpdGhQYXNzd29yZCh7XG4gICAgICAgIGVtYWlsOiB1c2VyPy5lbWFpbCB8fCAnJyxcbiAgICAgICAgcGFzc3dvcmQ6IHBhc3N3b3JkRGF0YS5jdXJyZW50UGFzc3dvcmRcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoc2lnbkluRXJyb3IpIHtcbiAgICAgICAgdG9hc3RFcnJvcignQ3VycmVudCBwYXNzd29yZCBpcyBpbmNvcnJlY3QnKTtcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgLy8gSWYgY3VycmVudCBwYXNzd29yZCBpcyBjb3JyZWN0LCB1cGRhdGUgdG8gbmV3IHBhc3N3b3JkXG4gICAgICBjb25zdCB7IGVycm9yOiB1cGRhdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC51cGRhdGVVc2VyKHtcbiAgICAgICAgcGFzc3dvcmQ6IHBhc3N3b3JkRGF0YS5uZXdQYXNzd29yZFxuICAgICAgfSk7XG5cbiAgICAgIGlmICh1cGRhdGVFcnJvcikgdGhyb3cgdXBkYXRlRXJyb3I7XG5cbiAgICAgIGNvbnNvbGUubG9nKCdQYXNzd29yZCB1cGRhdGVkIHN1Y2Nlc3NmdWxseSwgc2hvd2luZyB0b2FzdC4uLicpO1xuICAgICAgc3VjY2VzcygnUGFzc3dvcmQgdXBkYXRlZCBzdWNjZXNzZnVsbHknLCAnWW91ciBwYXNzd29yZCBoYXMgYmVlbiBjaGFuZ2VkIHN1Y2Nlc3NmdWxseS4nKTtcbiAgICAgIHNldFBhc3N3b3JkRGF0YSh7XG4gICAgICAgIGN1cnJlbnRQYXNzd29yZDogJycsXG4gICAgICAgIG5ld1Bhc3N3b3JkOiAnJyxcbiAgICAgICAgY29uZmlybVBhc3N3b3JkOiAnJ1xuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignUGFzc3dvcmQgdXBkYXRlIGVycm9yOicsIGVycm9yKTtcbiAgICAgIHRvYXN0RXJyb3IoJ0ZhaWxlZCB0byB1cGRhdGUgcGFzc3dvcmQnLCBlcnJvci5tZXNzYWdlIHx8ICdQbGVhc2UgdHJ5IGFnYWluJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVOb3RpZmljYXRpb25VcGRhdGUgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0TG9hZGluZyh0cnVlKTtcblxuICAgIHRyeSB7XG4gICAgICAvLyBTYXZlIHRvIGxvY2FsIHN0b3JhZ2UgYW5kIHVzZXIgbWV0YWRhdGFcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdub3RpZmljYXRpb25fc2V0dGluZ3MnLCBKU09OLnN0cmluZ2lmeShub3RpZmljYXRpb25TZXR0aW5ncykpO1xuXG4gICAgICAvLyBBbHNvIHNhdmUgdG8gdXNlciBtZXRhZGF0YSBmb3IgcGVyc2lzdGVuY2UgYWNyb3NzIGRldmljZXNcbiAgICAgIGNvbnN0IHsgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGgudXBkYXRlVXNlcih7XG4gICAgICAgIGRhdGE6IHtcbiAgICAgICAgICBub3RpZmljYXRpb25fcHJlZmVyZW5jZXM6IG5vdGlmaWNhdGlvblNldHRpbmdzXG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICBpZiAoZXJyb3IpIHRocm93IGVycm9yO1xuXG4gICAgICBzdWNjZXNzKCdOb3RpZmljYXRpb24gcHJlZmVyZW5jZXMgc2F2ZWQgc3VjY2Vzc2Z1bGx5Jyk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignTm90aWZpY2F0aW9uIHVwZGF0ZSBlcnJvcjonLCBlcnJvcik7XG4gICAgICB0b2FzdEVycm9yKCdGYWlsZWQgdG8gc2F2ZSBub3RpZmljYXRpb24gcHJlZmVyZW5jZXMnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVBhc3N3b3JkUmVzZXQgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF1c2VyPy5lbWFpbCkge1xuICAgICAgdG9hc3RFcnJvcignTm8gZW1haWwgYWRkcmVzcyBmb3VuZCcpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHNldElzUmVzZXRMb2FkaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnJlc2V0UGFzc3dvcmRGb3JFbWFpbCh1c2VyLmVtYWlsLCB7XG4gICAgICAgIHJlZGlyZWN0VG86IGAke3dpbmRvdy5sb2NhdGlvbi5vcmlnaW59L2F1dGgvcmVzZXQtcGFzc3dvcmRgXG4gICAgICB9KTtcblxuICAgICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvcjtcblxuICAgICAgc3VjY2VzcygnUGFzc3dvcmQgcmVzZXQgZW1haWwgc2VudCEnLCAnQ2hlY2sgeW91ciBpbmJveCBmb3IgaW5zdHJ1Y3Rpb25zIHRvIHJlc2V0IHlvdXIgcGFzc3dvcmQuJyk7XG4gICAgICBzZXRTaG93UmVzZXRNb2RhbChmYWxzZSk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignUGFzc3dvcmQgcmVzZXQgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgdG9hc3RFcnJvcignRmFpbGVkIHRvIHNlbmQgcmVzZXQgZW1haWwnLCBlcnJvci5tZXNzYWdlIHx8ICdQbGVhc2UgdHJ5IGFnYWluLicpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc1Jlc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUVtYWlsQ2hhbmdlID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghZW1haWxDaGFuZ2VEYXRhLm5ld0VtYWlsLnRyaW0oKSkge1xuICAgICAgdG9hc3RFcnJvcignUGxlYXNlIGVudGVyIGEgbmV3IGVtYWlsIGFkZHJlc3MnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAoIWVtYWlsQ2hhbmdlRGF0YS5jdXJyZW50UGFzc3dvcmQudHJpbSgpKSB7XG4gICAgICB0b2FzdEVycm9yKCdDdXJyZW50IHBhc3N3b3JkIGlzIHJlcXVpcmVkIHRvIGNoYW5nZSBlbWFpbCcpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGlmIChlbWFpbENoYW5nZURhdGEubmV3RW1haWwgPT09IHVzZXI/LmVtYWlsKSB7XG4gICAgICB0b2FzdEVycm9yKCdOZXcgZW1haWwgbXVzdCBiZSBkaWZmZXJlbnQgZnJvbSBjdXJyZW50IGVtYWlsJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gQmFzaWMgZW1haWwgdmFsaWRhdGlvblxuICAgIGNvbnN0IGVtYWlsUmVnZXggPSAvXlteXFxzQF0rQFteXFxzQF0rXFwuW15cXHNAXSskLztcbiAgICBpZiAoIWVtYWlsUmVnZXgudGVzdChlbWFpbENoYW5nZURhdGEubmV3RW1haWwpKSB7XG4gICAgICB0b2FzdEVycm9yKCdQbGVhc2UgZW50ZXIgYSB2YWxpZCBlbWFpbCBhZGRyZXNzJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0SXNFbWFpbExvYWRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEZpcnN0IHZlcmlmeSBjdXJyZW50IHBhc3N3b3JkXG4gICAgICBjb25zdCB7IGVycm9yOiBzaWduSW5FcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduSW5XaXRoUGFzc3dvcmQoe1xuICAgICAgICBlbWFpbDogdXNlcj8uZW1haWwgfHwgJycsXG4gICAgICAgIHBhc3N3b3JkOiBlbWFpbENoYW5nZURhdGEuY3VycmVudFBhc3N3b3JkXG4gICAgICB9KTtcblxuICAgICAgaWYgKHNpZ25JbkVycm9yKSB7XG4gICAgICAgIHRvYXN0RXJyb3IoJ0N1cnJlbnQgcGFzc3dvcmQgaXMgaW5jb3JyZWN0Jyk7XG4gICAgICAgIHNldElzRW1haWxMb2FkaW5nKGZhbHNlKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICAvLyBVcGRhdGUgZW1haWxcbiAgICAgIGNvbnN0IHsgZXJyb3I6IHVwZGF0ZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLnVwZGF0ZVVzZXIoe1xuICAgICAgICBlbWFpbDogZW1haWxDaGFuZ2VEYXRhLm5ld0VtYWlsXG4gICAgICB9KTtcblxuICAgICAgaWYgKHVwZGF0ZUVycm9yKSB0aHJvdyB1cGRhdGVFcnJvcjtcblxuICAgICAgc3VjY2VzcygnRW1haWwgY2hhbmdlIGluaXRpYXRlZCEnLCAnQ2hlY2sgYm90aCB5b3VyIG9sZCBhbmQgbmV3IGVtYWlsIGFkZHJlc3NlcyBmb3IgY29uZmlybWF0aW9uIGluc3RydWN0aW9ucy4nKTtcbiAgICAgIHNldFNob3dDaGFuZ2VFbWFpbE1vZGFsKGZhbHNlKTtcbiAgICAgIHNldEVtYWlsQ2hhbmdlRGF0YSh7IG5ld0VtYWlsOiAnJywgY3VycmVudFBhc3N3b3JkOiAnJyB9KTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFbWFpbCBjaGFuZ2UgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgdG9hc3RFcnJvcignRmFpbGVkIHRvIGNoYW5nZSBlbWFpbCcsIGVycm9yLm1lc3NhZ2UgfHwgJ1BsZWFzZSB0cnkgYWdhaW4uJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzRW1haWxMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQWNjb3VudERlbGV0aW9uID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghdXNlcikgcmV0dXJuO1xuXG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICB0cnkge1xuICAgICAgLy8gQ2FsbCBvdXIgYWNjb3VudCBkZWxldGlvbiBBUElcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvdXNlci9kZWxldGUtYWNjb3VudCcsIHtcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcbiAgICAgICAgfVxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLmVycm9yIHx8ICdGYWlsZWQgdG8gZGVsZXRlIGFjY291bnQnKTtcbiAgICAgIH1cblxuICAgICAgc3VjY2VzcygnQWNjb3VudCBkZWxldGVkIHN1Y2Nlc3NmdWxseS4gWW91IHdpbGwgYmUgcmVkaXJlY3RlZCB0byB0aGUgaG9tZXBhZ2UuJyk7XG5cbiAgICAgIC8vIFNpZ24gb3V0IGFuZCByZWRpcmVjdFxuICAgICAgYXdhaXQgc3VwYWJhc2UuYXV0aC5zaWduT3V0KCk7XG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnLyc7XG4gICAgICB9LCAyMDAwKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0FjY291bnQgZGVsZXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgICAgdG9hc3RFcnJvcignRmFpbGVkIHRvIGRlbGV0ZSBhY2NvdW50JywgZXJyb3IubWVzc2FnZSB8fCAnUGxlYXNlIGNvbnRhY3Qgc3VwcG9ydC4nKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctWyMwNDA3MTZdIHRleHQtd2hpdGVcIj5cbiAgICAgIHsvKiBIZWFkZXIgU2VjdGlvbiAtIEZvbGxvd2luZyBhbmFseXRpY3MvcGxheWdyb3VuZCBkZXNpZ24gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1ncmF5LTgwMC81MFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBweC00IHNtOnB4LTYgbGc6cHgtOCBweS02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+XG4gICAgICAgICAgICAgICAgQWNjb3VudCBTZXR0aW5ncyDimpnvuI9cbiAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LWxnXCI+XG4gICAgICAgICAgICAgICAgTWFuYWdlIHlvdXIgYWNjb3VudCBzZXR0aW5ncyBhbmQgcHJlZmVyZW5jZXNcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBOYXZpZ2F0aW9uIFRhYnMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci1iIGJvcmRlci1ncmF5LTgwMC81MFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwiLW1iLXB4IGZsZXggc3BhY2UteC04XCI+XG4gICAgICAgICAgICB7c2lkZWJhckl0ZW1zLm1hcCgoaXRlbSkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBJY29uID0gaXRlbS5pY29uO1xuICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIGtleT17aXRlbS5pZH1cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVNlY3Rpb24oaXRlbS5pZCl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweS00IHB4LTEgYm9yZGVyLWItMiBmb250LW1lZGl1bSB0ZXh0LXNtIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZVNlY3Rpb24gPT09IGl0ZW0uaWRcbiAgICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItb3JhbmdlLTUwMCB0ZXh0LW9yYW5nZS00MDAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLXRyYW5zcGFyZW50IHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTMwMCBob3Zlcjpib3JkZXItZ3JheS02MDAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8SWNvbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgIHtpdGVtLmxhYmVsfVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgfSl9XG4gICAgICAgICAgPC9uYXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDb250ZW50IEFyZWEgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBweC00IHNtOnB4LTYgbGc6cHgtOCBweS04XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsXCI+XG4gICAgICAgICAgICB7YWN0aXZlU2VjdGlvbiA9PT0gJ2FjY291bnQnICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTggbWF4LXctN3hsIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICB7LyogRW1haWwgQWRkcmVzcyBTZWN0aW9uICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS05MDAvNTAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS04MDAvNTAgcC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgYmctb3JhbmdlLTUwMC8xMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEVudmVsb3BlSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtb3JhbmdlLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5FbWFpbCBhZGRyZXNzPC9oMz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+WW91ciBlbWFpbCBhZGRyZXNzIGlzIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtd2hpdGVcIj57dXNlcj8uZW1haWx9PC9zcGFuPjwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDAgbXQtMVwiPlRoaXMgaXMgdXNlZCBmb3IgbG9naW4gYW5kIGltcG9ydGFudCBub3RpZmljYXRpb25zPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtb3JhbmdlLTQwMCBob3Zlcjp0ZXh0LW9yYW5nZS0zMDAgaG92ZXI6Ymctb3JhbmdlLTUwMC8xMCByb3VuZGVkLW1kIHRyYW5zaXRpb24tY29sb3JzIGJvcmRlciBib3JkZXItb3JhbmdlLTUwMC8yMCBob3Zlcjpib3JkZXItb3JhbmdlLTUwMC80MFwiXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0NoYW5nZUVtYWlsTW9kYWwodHJ1ZSl9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICBDaGFuZ2VcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBBY2NvdW50IEluZm9ybWF0aW9uICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS05MDAvNTAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS04MDAvNTAgcC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgYmctYmx1ZS01MDAvMTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxVc2VySWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtYmx1ZS00MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+QWNjb3VudCBJbmZvcm1hdGlvbjwvaDM+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlIG1iLTFcIj5GdWxsIE5hbWU8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNlcj8udXNlcl9tZXRhZGF0YT8uZnVsbF9uYW1lIHx8IHVzZXI/LnVzZXJfbWV0YWRhdGE/LmZpcnN0X25hbWUgKyAnICcgKyB1c2VyPy51c2VyX21ldGFkYXRhPy5sYXN0X25hbWUgfHwgJ05vdCBzZXQnfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlIG1iLTFcIj5BY2NvdW50IENyZWF0ZWQ8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNlcj8uY3JlYXRlZF9hdCA/IG5ldyBEYXRlKHVzZXIuY3JlYXRlZF9hdCkudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB5ZWFyOiAnbnVtZXJpYycsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbW9udGg6ICdsb25nJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXk6ICdudW1lcmljJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9KSA6ICdVbmtub3duJ31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUgbWItMVwiPkFjY291bnQgVXNhZ2U8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTEgdGV4dC1zbSB0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwPnt1c2VyU3RhdHMuY29uZmlnQ291bnR9IGNvbmZpZ3VyYXRpb25zPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cD57dXNlclN0YXRzLmFwaUtleUNvdW50fSBBUEkga2V5czwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+e3VzZXJTdGF0cy51c2VyQXBpS2V5Q291bnR9IHVzZXItZ2VuZXJhdGVkIGtleXM8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBQYXNzd29yZCBTZWN0aW9uICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS05MDAvNTAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JheS04MDAvNTAgcC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgYmctZ3JlZW4tNTAwLzEwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8S2V5SWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JlZW4tNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZVwiPlBhc3N3b3JkPC9oMz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVBhc3N3b3JkQ2hhbmdlfSBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImN1cnJlbnRQYXNzd29yZFwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPkN1cnJlbnQgcGFzc3dvcmQ8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbXQtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlkPVwiY3VycmVudFBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT17c2hvd0N1cnJlbnRQYXNzd29yZCA/IFwidGV4dFwiIDogXCJwYXNzd29yZFwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cGFzc3dvcmREYXRhLmN1cnJlbnRQYXNzd29yZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRQYXNzd29yZERhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBjdXJyZW50UGFzc3dvcmQ6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLigKLigKLigKLigKLigKLigKLigKLigKLigKLigKJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHItMTAgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS02MDAgcm91bmRlZC1sZyB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNDAwIGJnLWdyYXktODAwLzUwIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9yYW5nZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0N1cnJlbnRQYXNzd29yZCghc2hvd0N1cnJlbnRQYXNzd29yZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS0zMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7c2hvd0N1cnJlbnRQYXNzd29yZCA/IDxFeWVTbGFzaEljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+IDogPEV5ZUljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+fVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJuZXdQYXNzd29yZFwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPk5ldyBwYXNzd29yZDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG10LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJuZXdQYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT17c2hvd05ld1Bhc3N3b3JkID8gXCJ0ZXh0XCIgOiBcInBhc3N3b3JkXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Bhc3N3b3JkRGF0YS5uZXdQYXNzd29yZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFBhc3N3b3JkRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIG5ld1Bhc3N3b3JkOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLigKLigKLigKLigKLigKLigKLigKLigKLigKLigKJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBwci0xMCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTYwMCByb3VuZGVkLWxnIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDAgYmctZ3JheS04MDAvNTAgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb3JhbmdlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd05ld1Bhc3N3b3JkKCFzaG93TmV3UGFzc3dvcmQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHJpZ2h0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS0zMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3Nob3dOZXdQYXNzd29yZCA/IDxFeWVTbGFzaEljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+IDogPEV5ZUljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+fVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImNvbmZpcm1QYXNzd29yZFwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPkNvbmZpcm0gbmV3IHBhc3N3b3JkPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbXQtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZD1cImNvbmZpcm1QYXNzd29yZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT17c2hvd0NvbmZpcm1QYXNzd29yZCA/IFwidGV4dFwiIDogXCJwYXNzd29yZFwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtwYXNzd29yZERhdGEuY29uZmlybVBhc3N3b3JkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UGFzc3dvcmREYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgY29uZmlybVBhc3N3b3JkOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLigKLigKLigKLigKLigKLigKLigKLigKLigKLigKJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBwci0xMCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTYwMCByb3VuZGVkLWxnIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDAgYmctZ3JheS04MDAvNTAgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb3JhbmdlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0NvbmZpcm1QYXNzd29yZCghc2hvd0NvbmZpcm1QYXNzd29yZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTMwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2hvd0NvbmZpcm1QYXNzd29yZCA/IDxFeWVTbGFzaEljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+IDogPEV5ZUljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+fVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgQ2FuJ3QgcmVtZW1iZXIgeW91ciBjdXJyZW50IHBhc3N3b3JkPyA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LW9yYW5nZS00MDAgaG92ZXI6dGV4dC1vcmFuZ2UtMzAwIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1Jlc2V0TW9kYWwodHJ1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFJlc2V0IHlvdXIgcGFzc3dvcmRcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gdHlwZT1cInN1Ym1pdFwiIGRpc2FibGVkPXtsb2FkaW5nfSBjbGFzc05hbWU9XCJiZy1vcmFuZ2UtNjAwIGhvdmVyOmJnLW9yYW5nZS03MDAgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2xvYWRpbmcgPyAnU2F2aW5nLi4uJyA6ICdTYXZlIHBhc3N3b3JkJ31cbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAge2FjdGl2ZVNlY3Rpb24gPT09ICdub3RpZmljYXRpb25zJyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04IG1heC13LTd4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgey8qIEVtYWlsIE5vdGlmaWNhdGlvbnMgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMC81MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTgwMC81MCBwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiBiZy1ibHVlLTUwMC8xMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEJlbGxJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ibHVlLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5Ob3RpZmljYXRpb24gUHJlZmVyZW5jZXM8L2gzPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlXCI+RW1haWwgTm90aWZpY2F0aW9uczwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5SZWNlaXZlIGltcG9ydGFudCB1cGRhdGVzIHZpYSBlbWFpbDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwicmVsYXRpdmUgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGN1cnNvci1wb2ludGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17bm90aWZpY2F0aW9uU2V0dGluZ3MuZW1haWxOb3RpZmljYXRpb25zfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldE5vdGlmaWNhdGlvblNldHRpbmdzKHByZXYgPT4gKHsgLi4ucHJldiwgZW1haWxOb3RpZmljYXRpb25zOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic3Itb25seSBwZWVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTEgaC02IGJnLWdyYXktNjAwIHBlZXItZm9jdXM6b3V0bGluZS1ub25lIHBlZXItZm9jdXM6cmluZy00IHBlZXItZm9jdXM6cmluZy1vcmFuZ2UtMzAwLzIwIHJvdW5kZWQtZnVsbCBwZWVyIHBlZXItY2hlY2tlZDphZnRlcjp0cmFuc2xhdGUteC1mdWxsIHBlZXItY2hlY2tlZDphZnRlcjpib3JkZXItd2hpdGUgYWZ0ZXI6Y29udGVudC1bJyddIGFmdGVyOmFic29sdXRlIGFmdGVyOnRvcC1bMnB4XSBhZnRlcjpsZWZ0LVsycHhdIGFmdGVyOmJnLXdoaXRlIGFmdGVyOnJvdW5kZWQtZnVsbCBhZnRlcjpoLTUgYWZ0ZXI6dy01IGFmdGVyOnRyYW5zaXRpb24tYWxsIHBlZXItY2hlY2tlZDpiZy1vcmFuZ2UtNjAwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGVcIj5TZWN1cml0eSBBbGVydHM8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+R2V0IG5vdGlmaWVkIGFib3V0IHNlY3VyaXR5IGV2ZW50czwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwicmVsYXRpdmUgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGN1cnNvci1wb2ludGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17bm90aWZpY2F0aW9uU2V0dGluZ3Muc2VjdXJpdHlBbGVydHN9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Tm90aWZpY2F0aW9uU2V0dGluZ3MocHJldiA9PiAoeyAuLi5wcmV2LCBzZWN1cml0eUFsZXJ0czogZS50YXJnZXQuY2hlY2tlZCB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNyLW9ubHkgcGVlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTExIGgtNiBiZy1ncmF5LTYwMCBwZWVyLWZvY3VzOm91dGxpbmUtbm9uZSBwZWVyLWZvY3VzOnJpbmctNCBwZWVyLWZvY3VzOnJpbmctb3JhbmdlLTMwMC8yMCByb3VuZGVkLWZ1bGwgcGVlciBwZWVyLWNoZWNrZWQ6YWZ0ZXI6dHJhbnNsYXRlLXgtZnVsbCBwZWVyLWNoZWNrZWQ6YWZ0ZXI6Ym9yZGVyLXdoaXRlIGFmdGVyOmNvbnRlbnQtWycnXSBhZnRlcjphYnNvbHV0ZSBhZnRlcjp0b3AtWzJweF0gYWZ0ZXI6bGVmdC1bMnB4XSBhZnRlcjpiZy13aGl0ZSBhZnRlcjpyb3VuZGVkLWZ1bGwgYWZ0ZXI6aC01IGFmdGVyOnctNSBhZnRlcjp0cmFuc2l0aW9uLWFsbCBwZWVyLWNoZWNrZWQ6Ymctb3JhbmdlLTYwMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlXCI+VXNhZ2UgQWxlcnRzPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTQwMFwiPk5vdGlmaWNhdGlvbnMgYWJvdXQgQVBJIHVzYWdlIGFuZCBsaW1pdHM8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInJlbGF0aXZlIGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBjdXJzb3ItcG9pbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e25vdGlmaWNhdGlvblNldHRpbmdzLnVzYWdlQWxlcnRzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldE5vdGlmaWNhdGlvblNldHRpbmdzKHByZXYgPT4gKHsgLi4ucHJldiwgdXNhZ2VBbGVydHM6IGUudGFyZ2V0LmNoZWNrZWQgfSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzci1vbmx5IHBlZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMSBoLTYgYmctZ3JheS02MDAgcGVlci1mb2N1czpvdXRsaW5lLW5vbmUgcGVlci1mb2N1czpyaW5nLTQgcGVlci1mb2N1czpyaW5nLW9yYW5nZS0zMDAvMjAgcm91bmRlZC1mdWxsIHBlZXIgcGVlci1jaGVja2VkOmFmdGVyOnRyYW5zbGF0ZS14LWZ1bGwgcGVlci1jaGVja2VkOmFmdGVyOmJvcmRlci13aGl0ZSBhZnRlcjpjb250ZW50LVsnJ10gYWZ0ZXI6YWJzb2x1dGUgYWZ0ZXI6dG9wLVsycHhdIGFmdGVyOmxlZnQtWzJweF0gYWZ0ZXI6Ymctd2hpdGUgYWZ0ZXI6cm91bmRlZC1mdWxsIGFmdGVyOmgtNSBhZnRlcjp3LTUgYWZ0ZXI6dHJhbnNpdGlvbi1hbGwgcGVlci1jaGVja2VkOmJnLW9yYW5nZS02MDBcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPk1hcmtldGluZyBFbWFpbHM8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNDAwXCI+UHJvZHVjdCB1cGRhdGVzIGFuZCBmZWF0dXJlIGFubm91bmNlbWVudHM8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInJlbGF0aXZlIGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBjdXJzb3ItcG9pbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e25vdGlmaWNhdGlvblNldHRpbmdzLm1hcmtldGluZ0VtYWlsc31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROb3RpZmljYXRpb25TZXR0aW5ncyhwcmV2ID0+ICh7IC4uLnByZXYsIG1hcmtldGluZ0VtYWlsczogZS50YXJnZXQuY2hlY2tlZCB9KSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNyLW9ubHkgcGVlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTExIGgtNiBiZy1ncmF5LTYwMCBwZWVyLWZvY3VzOm91dGxpbmUtbm9uZSBwZWVyLWZvY3VzOnJpbmctNCBwZWVyLWZvY3VzOnJpbmctb3JhbmdlLTMwMC8yMCByb3VuZGVkLWZ1bGwgcGVlciBwZWVyLWNoZWNrZWQ6YWZ0ZXI6dHJhbnNsYXRlLXgtZnVsbCBwZWVyLWNoZWNrZWQ6YWZ0ZXI6Ym9yZGVyLXdoaXRlIGFmdGVyOmNvbnRlbnQtWycnXSBhZnRlcjphYnNvbHV0ZSBhZnRlcjp0b3AtWzJweF0gYWZ0ZXI6bGVmdC1bMnB4XSBhZnRlcjpiZy13aGl0ZSBhZnRlcjpyb3VuZGVkLWZ1bGwgYWZ0ZXI6aC01IGFmdGVyOnctNSBhZnRlcjp0cmFuc2l0aW9uLWFsbCBwZWVyLWNoZWNrZWQ6Ymctb3JhbmdlLTYwMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgcHQtNiBib3JkZXItdCBib3JkZXItZ3JheS03MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZU5vdGlmaWNhdGlvblVwZGF0ZX1cbiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1vcmFuZ2UtNjAwIGhvdmVyOmJnLW9yYW5nZS03MDAgdGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7bG9hZGluZyA/ICdTYXZpbmcuLi4nIDogJ1NhdmUgUHJlZmVyZW5jZXMnfVxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHthY3RpdmVTZWN0aW9uID09PSAnc2VjdXJpdHknICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTggbWF4LXctN3hsIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICB7LyogU2VjdXJpdHkgT3ZlcnZpZXcgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMC81MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTgwMC81MCBwLTZcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMiBiZy1ncmVlbi01MDAvMTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxTaGllbGRDaGVja0ljb24gY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWdyZWVuLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5TZWN1cml0eSBTZXR0aW5nczwvaDM+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNTAwLzEwIGJvcmRlciBib3JkZXItZ3JlZW4tNTAwLzIwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFNoaWVsZENoZWNrSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtZ3JlZW4tNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JlZW4tNDAwXCI+QWNjb3VudCBTZWN1cml0eSBTdGF0dXM8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JlZW4tMzAwXCI+WW91ciBhY2NvdW50IGlzIHNlY3VyZSBhbmQgcHJvdGVjdGVkPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktODAwLzUwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlIG1iLTJcIj5MYXN0IExvZ2luPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNlcj8ubGFzdF9zaWduX2luX2F0ID8gbmV3IERhdGUodXNlci5sYXN0X3NpZ25faW5fYXQpLnRvTG9jYWxlU3RyaW5nKCkgOiAnVW5rbm93bid9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC81MCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZSBtYi0yXCI+QWNjb3VudCBUeXBlPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTMwMFwiPkVtYWlsICYgUGFzc3dvcmQ8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAge2FjdGl2ZVNlY3Rpb24gPT09ICdkYW5nZXInICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTggbWF4LXctN3hsIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICB7LyogRGFuZ2VyIFpvbmUgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtOTAwLzIwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLXJlZC04MDAvNTAgcC02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgYmctcmVkLTUwMC8xMCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFRyYXNoSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtcmVkLTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5EYW5nZXIgWm9uZTwvaDM+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcmVkLTUwMC8xMCBib3JkZXIgYm9yZGVyLXJlZC01MDAvMjAgcm91bmRlZC1sZyBwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXJlZC00MDAgbWItMlwiPuKaoO+4jyBBY2NvdW50IERlbGV0aW9uPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTMwMCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBPbmNlIHlvdSBkZWxldGUgeW91ciBhY2NvdW50LCB0aGVyZSBpcyBubyBnb2luZyBiYWNrLiBUaGlzIHdpbGwgcGVybWFuZW50bHkgZGVsZXRlIHlvdXIgYWNjb3VudCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbmZpZ3VyYXRpb25zLCBBUEkga2V5cywgYW5kIGFsbCBhc3NvY2lhdGVkIGRhdGEuXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dEZWxldGVNb2RhbCh0cnVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXJlZC02MDAgaG92ZXI6YmctcmVkLTcwMCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICBEZWxldGUgQWNjb3VudFxuICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDaGFuZ2UgRW1haWwgTW9kYWwgKi99XG4gICAgICB7c2hvd0NoYW5nZUVtYWlsTW9kYWwgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2svNzAgYmFja2Ryb3AtYmx1ci1zbSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTQgei01MFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS05MDAgYm9yZGVyIGJvcmRlci1ncmF5LTgwMCByb3VuZGVkLXhsIHNoYWRvdy0yeGwgbWF4LXctbWQgdy1mdWxsIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBtYi00XCI+XG4gICAgICAgICAgICAgIDxFbnZlbG9wZUljb24gY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LW9yYW5nZS00MDBcIiAvPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj5DaGFuZ2UgRW1haWwgQWRkcmVzczwvaDM+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJuZXdFbWFpbFwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPk5ldyBFbWFpbCBBZGRyZXNzPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIGlkPVwibmV3RW1haWxcIlxuICAgICAgICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtlbWFpbENoYW5nZURhdGEubmV3RW1haWx9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEVtYWlsQ2hhbmdlRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIG5ld0VtYWlsOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIG5ldyBlbWFpbCBhZGRyZXNzXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBtdC0yIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbGcgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci1ncmF5LTQwMCBiZy1ncmF5LTgwMC81MCBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vcmFuZ2UtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjdXJyZW50UGFzc3dvcmRFbWFpbFwiIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPkN1cnJlbnQgUGFzc3dvcmQ8L0xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgaWQ9XCJjdXJyZW50UGFzc3dvcmRFbWFpbFwiXG4gICAgICAgICAgICAgICAgICB0eXBlPVwicGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2VtYWlsQ2hhbmdlRGF0YS5jdXJyZW50UGFzc3dvcmR9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEVtYWlsQ2hhbmdlRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIGN1cnJlbnRQYXNzd29yZDogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBjdXJyZW50IHBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBtdC0yIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbGcgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci1ncmF5LTQwMCBiZy1ncmF5LTgwMC81MCBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vcmFuZ2UtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMyBtdC02XCI+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0NoYW5nZUVtYWlsTW9kYWwoZmFsc2UpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSB0ZXh0LWdyYXktMzAwIGJvcmRlci1ncmF5LTYwMCBob3ZlcjpiZy1ncmF5LTgwMC81MFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVFbWFpbENoYW5nZX1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNFbWFpbExvYWRpbmd9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGJnLW9yYW5nZS02MDAgaG92ZXI6Ymctb3JhbmdlLTcwMCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpc0VtYWlsTG9hZGluZyA/ICdDaGFuZ2luZy4uLicgOiAnQ2hhbmdlIEVtYWlsJ31cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogUmVzZXQgUGFzc3dvcmQgTW9kYWwgKi99XG4gICAgICB7c2hvd1Jlc2V0TW9kYWwgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2svNzAgYmFja2Ryb3AtYmx1ci1zbSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTQgei01MFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS05MDAgYm9yZGVyIGJvcmRlci1ncmF5LTgwMCByb3VuZGVkLXhsIHNoYWRvdy0yeGwgbWF4LXctbWQgdy1mdWxsIHAtNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBtYi00XCI+XG4gICAgICAgICAgICAgIDxLZXlJY29uIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1ibHVlLTQwMFwiIC8+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZVwiPlJlc2V0IFBhc3N3b3JkPC9oMz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBtYi00XCI+XG4gICAgICAgICAgICAgIFdlJ2xsIHNlbmQgYSBwYXNzd29yZCByZXNldCBsaW5rIHRvIHlvdXIgZW1haWwgYWRkcmVzcy5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtM1wiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dSZXNldE1vZGFsKGZhbHNlKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgdGV4dC1ncmF5LTMwMCBib3JkZXItZ3JheS02MDAgaG92ZXI6YmctZ3JheS04MDAvNTBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgQ2FuY2VsXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlUGFzc3dvcmRSZXNldH1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNSZXNldExvYWRpbmd9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIHRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2lzUmVzZXRMb2FkaW5nID8gJ1NlbmRpbmcuLi4nIDogJ1NlbmQgUmVzZXQgTGluayd9XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIERlbGV0ZSBBY2NvdW50IE1vZGFsICovfVxuICAgICAge3Nob3dEZWxldGVNb2RhbCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjay83MCBiYWNrZHJvcC1ibHVyLXNtIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtNCB6LTUwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMCBib3JkZXIgYm9yZGVyLXJlZC04MDAvNTAgcm91bmRlZC14bCBzaGFkb3ctMnhsIG1heC13LW1kIHctZnVsbCBwLTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgbWItNFwiPlxuICAgICAgICAgICAgICA8VHJhc2hJY29uIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1yZWQtNDAwXCIgLz5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+RGVsZXRlIEFjY291bnQ8L2gzPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC01MDAvMTAgYm9yZGVyIGJvcmRlci1yZWQtNTAwLzIwIHJvdW5kZWQtbGcgcC00IG1iLTRcIj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1yZWQtMzAwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICDimqDvuI8gPHN0cm9uZz5UaGlzIGFjdGlvbiBjYW5ub3QgYmUgdW5kb25lLjwvc3Ryb25nPiBUaGlzIHdpbGwgcGVybWFuZW50bHkgZGVsZXRlIHlvdXIgYWNjb3VudCxcbiAgICAgICAgICAgICAgICBhbGwgY29uZmlndXJhdGlvbnMsIEFQSSBrZXlzLCBhbmQgYXNzb2NpYXRlZCBkYXRhLlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgbWItNlwiPlxuICAgICAgICAgICAgICBBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlIHlvdXIgYWNjb3VudD8gVHlwZSBcIkRFTEVURVwiIHRvIGNvbmZpcm0uXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlR5cGUgREVMRVRFIHRvIGNvbmZpcm1cIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS02MDAgcm91bmRlZC1sZyB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNDAwIGJnLWdyYXktODAwLzUwIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXJlZC01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IG1iLTRcIlxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAvLyBZb3UgY2FuIGFkZCBjb25maXJtYXRpb24gbG9naWMgaGVyZVxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtM1wiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dEZWxldGVNb2RhbChmYWxzZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHRleHQtZ3JheS0zMDAgYm9yZGVyLWdyYXktNjAwIGhvdmVyOmJnLWdyYXktODAwLzUwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUFjY291bnREZWxldGlvbn1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgYmctcmVkLTYwMCBob3ZlcjpiZy1yZWQtNzAwIHRleHQtd2hpdGVcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2xvYWRpbmcgPyAnRGVsZXRpbmcuLi4nIDogJ0RlbGV0ZSBBY2NvdW50J31cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUm91dGVyIiwiVXNlckljb24iLCJLZXlJY29uIiwiU2hpZWxkQ2hlY2tJY29uIiwiQmVsbEljb24iLCJFeWVJY29uIiwiRXllU2xhc2hJY29uIiwiVHJhc2hJY29uIiwiRW52ZWxvcGVJY29uIiwiQnV0dG9uIiwiTGFiZWwiLCJ1c2VUb2FzdCIsImNyZWF0ZVN1cGFiYXNlQnJvd3NlckNsaWVudCIsInVzZVN1YnNjcmlwdGlvbiIsIlNldHRpbmdzUGFnZSIsInVzZXIiLCJyb3V0ZXIiLCJzdXBhYmFzZSIsInN1Y2Nlc3MiLCJlcnJvciIsInRvYXN0RXJyb3IiLCJhY3RpdmVTZWN0aW9uIiwic2V0QWN0aXZlU2VjdGlvbiIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic2hvd0N1cnJlbnRQYXNzd29yZCIsInNldFNob3dDdXJyZW50UGFzc3dvcmQiLCJzaG93TmV3UGFzc3dvcmQiLCJzZXRTaG93TmV3UGFzc3dvcmQiLCJzaG93Q29uZmlybVBhc3N3b3JkIiwic2V0U2hvd0NvbmZpcm1QYXNzd29yZCIsInNob3dEZWxldGVNb2RhbCIsInNldFNob3dEZWxldGVNb2RhbCIsInNob3dSZXNldE1vZGFsIiwic2V0U2hvd1Jlc2V0TW9kYWwiLCJpc1Jlc2V0TG9hZGluZyIsInNldElzUmVzZXRMb2FkaW5nIiwic2hvd0NoYW5nZUVtYWlsTW9kYWwiLCJzZXRTaG93Q2hhbmdlRW1haWxNb2RhbCIsImlzRW1haWxMb2FkaW5nIiwic2V0SXNFbWFpbExvYWRpbmciLCJlbWFpbENoYW5nZURhdGEiLCJzZXRFbWFpbENoYW5nZURhdGEiLCJuZXdFbWFpbCIsImN1cnJlbnRQYXNzd29yZCIsInBhc3N3b3JkRGF0YSIsInNldFBhc3N3b3JkRGF0YSIsIm5ld1Bhc3N3b3JkIiwiY29uZmlybVBhc3N3b3JkIiwibm90aWZpY2F0aW9uU2V0dGluZ3MiLCJzZXROb3RpZmljYXRpb25TZXR0aW5ncyIsImVtYWlsTm90aWZpY2F0aW9ucyIsInNlY3VyaXR5QWxlcnRzIiwidXNhZ2VBbGVydHMiLCJtYXJrZXRpbmdFbWFpbHMiLCJ1c2VyU3RhdHMiLCJzZXRVc2VyU3RhdHMiLCJjb25maWdDb3VudCIsImFwaUtleUNvdW50IiwidXNlckFwaUtleUNvdW50Iiwic2lkZWJhckl0ZW1zIiwiaWQiLCJsYWJlbCIsImljb24iLCJsb2FkVXNlclN0YXRzIiwibG9hZE5vdGlmaWNhdGlvblNldHRpbmdzIiwiY29uZmlnUmVzcG9uc2UiLCJmZXRjaCIsImNvbmZpZ0RhdGEiLCJvayIsImpzb24iLCJjb25maWdzIiwiQXJyYXkiLCJpc0FycmF5IiwidXNlcktleXNSZXNwb25zZSIsInVzZXJLZXlzRGF0YSIsImFwaV9rZXlzIiwidXNlcktleXMiLCJ0b3RhbEFwaUtleXMiLCJjb25maWciLCJrZXlzUmVzcG9uc2UiLCJrZXlzRGF0YSIsImxlbmd0aCIsImNvbnNvbGUiLCJzYXZlZFNldHRpbmdzIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIkpTT04iLCJwYXJzZSIsImhhbmRsZVBhc3N3b3JkQ2hhbmdlIiwiZSIsInByZXZlbnREZWZhdWx0IiwidHJpbSIsInNpZ25JbkVycm9yIiwiYXV0aCIsInNpZ25JbldpdGhQYXNzd29yZCIsImVtYWlsIiwicGFzc3dvcmQiLCJ1cGRhdGVFcnJvciIsInVwZGF0ZVVzZXIiLCJsb2ciLCJtZXNzYWdlIiwiaGFuZGxlTm90aWZpY2F0aW9uVXBkYXRlIiwic2V0SXRlbSIsInN0cmluZ2lmeSIsImRhdGEiLCJub3RpZmljYXRpb25fcHJlZmVyZW5jZXMiLCJoYW5kbGVQYXNzd29yZFJlc2V0IiwicmVzZXRQYXNzd29yZEZvckVtYWlsIiwicmVkaXJlY3RUbyIsIndpbmRvdyIsImxvY2F0aW9uIiwib3JpZ2luIiwiaGFuZGxlRW1haWxDaGFuZ2UiLCJlbWFpbFJlZ2V4IiwidGVzdCIsImhhbmRsZUFjY291bnREZWxldGlvbiIsInJlc3BvbnNlIiwibWV0aG9kIiwiaGVhZGVycyIsImVycm9yRGF0YSIsIkVycm9yIiwic2lnbk91dCIsInNldFRpbWVvdXQiLCJocmVmIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwibmF2IiwibWFwIiwiaXRlbSIsIkljb24iLCJidXR0b24iLCJvbkNsaWNrIiwiaDMiLCJzcGFuIiwiaDQiLCJ1c2VyX21ldGFkYXRhIiwiZnVsbF9uYW1lIiwiZmlyc3RfbmFtZSIsImxhc3RfbmFtZSIsImNyZWF0ZWRfYXQiLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwieWVhciIsIm1vbnRoIiwiZGF5IiwiZm9ybSIsIm9uU3VibWl0IiwiaHRtbEZvciIsImlucHV0IiwidHlwZSIsInZhbHVlIiwib25DaGFuZ2UiLCJwcmV2IiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJyZXF1aXJlZCIsImRpc2FibGVkIiwiY2hlY2tlZCIsImxhc3Rfc2lnbl9pbl9hdCIsInRvTG9jYWxlU3RyaW5nIiwidmFyaWFudCIsInN0cm9uZyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(app)/settings/page.tsx\n"));

/***/ })

});